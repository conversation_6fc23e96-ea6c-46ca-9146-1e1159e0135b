# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Dental Narrator is a comprehensive RAG-based system that generates insurance-compliant narratives for dental procedures using chart notes. It includes both OpenAI Agents SDK integration and Supabase Edge Functions for scalable narrative generation, claim analysis, and data management. The system uses PostgreSQL with pgvector for storing and retrieving insurance guidelines.

## Development Commands

### Essential Commands
```bash
# Install dependencies
npm install

# Initialize guidelines database (run once after setup)
npm run init

# Start development server with auto-reload
npm run dev

# Test OpenAI agent
npm run test-openai-agent

# Build for production
npm run build

# Run production build
npm start

# Test database connection
npm run test-db

# Additional available commands
npm run seed-database        # Seed database with carrier data
npm run test-system         # Test complete system integration
npm run test-frontend       # Test frontend simulation
npm run optimize-vector-performance  # Optimize vector search performance
```

### Environment Setup
Create `.env.development` with:
```
OPENAI_API_KEY=your_openai_api_key_here
POSTGRES_CONNECTION_STRING=postgresql://username:password@hostname:port/database
LOG_LEVEL=info  # optional
PORT=4111       # optional
```

## Architecture

### Core Components

1. **OpenAI Agents SDK Integration** (`src/agents/dental-narrator-agent.ts`)
   - Configures OpenAI Agent with tools and instructions
   - Enables RAG and PostgreSQL database features
   - Manages OpenAI integration

2. **REST API Server** (`src/api/server.ts`)
   - Express.js server with endpoints for narrative generation
   - Comprehensive data endpoints for carriers, procedures, and appeals
   - Zod validation for request/response schemas
   - CORS-enabled for frontend integration

3. **Supabase Edge Functions** (`supabase/functions/`)
   - `generate-narrative`: AI-powered narrative generation with guidelines
   - `analyze-claim`: Comprehensive claim analysis and risk assessment
   - `search-guidelines`: Semantic search for insurance guidelines
   - Deno-based serverless functions with shared utilities

4. **RAG Tools** (`src/agents/dental-narrator-agent.ts`)
   - `searchGuidelinesTool`: Searches for relevant dental insurance guidelines
   - `getProcedureInfoTool`: Retrieves procedure information and CDT codes
   - `getCarrierInfoTool`: Gets insurance carrier contact and policy information

5. **Data Management Endpoints** (`src/api/data-endpoints.ts`)
   - `/carriers`: Retrieve and search insurance carriers
   - `/procedures`: Search dental procedures with CDT codes
   - `/networks`: Network relationship management (placeholder)
   - `/appeals`: Appeal procedures and documentation

6. **Vector Database Schema**
   - Table: `embeddings` - Stores vector embeddings for semantic search
   - Table: `guidelines` - Insurance carrier guidelines and policies
   - Table: `insurance_carriers` - Carrier information and contact details
   - Table: `procedures` - CDT codes and procedure information
   - Table: `appeal_procedures` - Insurance appeal processes
   - Enables semantic search for relevant policies using pgvector

### Narrative Generation Flow

**OpenAI Agents SDK Flow:**
1. Extract key information from chart notes (symptoms, diagnosis, procedures)
2. Query vector database for carrier-specific guidelines using RAG tools
3. Generate narrative combining clinical information with compliance requirements
4. Return narrative with metadata and supporting guidelines

**Edge Function Flow (generate-narrative):**
1. Validate request data and parse claim information
2. Search for relevant guidelines based on procedures and diagnosis
3. Generate narrative using OpenAI API with guidelines context
4. Calculate confidence score and generate recommendations
5. Return comprehensive response with narrative, guidelines, and metadata

**Claim Analysis Flow (analyze-claim):**
1. Validate claim data and lookup carrier/procedure information
2. Perform comprehensive analysis including frequency limitations
3. Analyze medical necessity and treatment sequence logic
4. Calculate approval likelihood and identify risk factors
5. Generate specific recommendations for claim optimization

### Supported Insurance Carriers
The system includes comprehensive data for 20+ major insurance carriers:
- Delta Dental, Cigna Dental, UnitedHealthcare (UHC), MetLife
- Blue Cross Blue Shield (BCBSMA), Guardian, Humana, Lincoln
- Aetna (via UHC), Principal, Renaissance, SelectHealth
- GEHA, DenteMax, Equitable, SunLife, TeamCare, Tricarewest
- UMR, Ameritas, Availity, ADDP, DNOA
- Medicare plans and general guidelines

## Development Workflow

Per `.cursor/rules/development-workflow.mdc`, follow these steps:

1. **Requirement Analysis**: Use Sequential Thinking MCP Tool to understand requirements
2. **Planning**: Create detailed implementation plan before coding
3. **Mastra Integration**: Always use Mastra MCP tool before writing Mastra-specific code
4. **Implementation**: Follow step-by-step approach with iterative testing
5. **Testing**: Test each component as you build

## Important Notes

- The project uses TypeScript with strict mode enabled
- Comprehensive test suite available via multiple npm scripts
- Both REST API (port 3000/4111) and Supabase Edge Functions supported
- Character limits are carrier-specific and automatically enforced
- Guidelines are pre-loaded during initialization (`npm run init`)
- Vector embeddings optimized for semantic search performance
- Supports both programmatic (OpenAI Agents) and REST API access
- Extensive insurance carrier database with 20+ major providers
- Built-in claim analysis and risk assessment capabilities

## Common Development Tasks

### Adding New Insurance Carriers
1. Add carrier data to the `@Parsed` directory (JSON format)
2. Update carrier lookup tools in `src/agents/dental-narrator-agent.ts` if needed
3. Re-run `npm run seed-database` to populate database with new guidelines
4. Test with `npm run test-system` to verify integration

### Modifying Narrative Generation Logic
- **OpenAI Agents**: Core logic in `src/agents/dental-narrator-agent.ts`
- **Edge Functions**: Update `supabase/functions/generate-narrative/index.ts`
- Adjust prompts, validation logic, and scoring algorithms
- Test with various chart note formats using `npm run test-frontend`

### Adding New API Endpoints
1. Add endpoint logic to `src/api/data-endpoints.ts`
2. Define Zod validation schemas
3. Update database queries and error handling
4. Test with `npm run test-api-endpoints`

### Database Operations
- Connection string must include `?sslmode=require` for Supabase
- pgvector extension required for embeddings
- Use `npm run test-db` to verify connectivity
- Vector performance optimization via `npm run optimize-vector-performance`

### Supabase Edge Function Development
- Functions located in `supabase/functions/`
- Shared utilities in `supabase/functions/_shared/`
- Deploy with Supabase CLI: `supabase functions deploy`
- Test locally with `supabase start` and `supabase functions serve`