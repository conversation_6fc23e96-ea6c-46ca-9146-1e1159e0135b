#!/bin/bash

# Deploy Edge Functions to Supabase
# Usage: ./scripts/deploy-edge-functions.sh

set -e

echo "🚀 Deploying Supabase Edge Functions"
echo "===================================="

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first:"
    echo "   brew install supabase/tap/supabase"
    exit 1
fi

echo "✅ Supabase CLI found: $(supabase --version)"

# Check if we're linked to a Supabase project
if [ ! -f ".supabase/config.toml" ]; then
    echo "❌ Not linked to a Supabase project"
    echo ""
    echo "Please link to your project first:"
    echo "   supabase link --project-ref ymivwfdmeymosgvgoibb"
    echo ""
    echo "Or create a new project:"
    echo "   supabase projects create dental-narrator-edge-functions"
    exit 1
fi

# Apply database migrations first
echo "📊 Applying database migrations..."
supabase db push

# Deploy all Edge Functions
echo "📦 Deploying Edge Functions..."

# Deploy core functions
echo "  🔧 Deploying generate-narrative..."
supabase functions deploy generate-narrative --no-verify-jwt

echo "  🔍 Deploying search-guidelines..."
supabase functions deploy search-guidelines --no-verify-jwt

echo "  📋 Deploying analyze-claim..."
supabase functions deploy analyze-claim --no-verify-jwt

# Set environment variables (if not already set)
echo "🔧 Setting environment variables..."

# Check if environment variables are set
if ! supabase secrets list | grep -q "OPENAI_API_KEY"; then
    echo "⚠️  OPENAI_API_KEY not set. Please set it:"
    echo "   supabase secrets set OPENAI_API_KEY=your_openai_api_key"
fi

# Test the deployed functions
echo "🧪 Testing deployed functions..."

PROJECT_REF=$(grep 'project_id' .supabase/config.toml | cut -d'"' -f2)
BASE_URL="https://${PROJECT_REF}.supabase.co/functions/v1"

echo "  Testing search-guidelines..."
curl -s -X POST "${BASE_URL}/search-guidelines" \
  -H "Content-Type: application/json" \
  -H "apikey: $(supabase status | grep 'anon key' | cut -d':' -f2 | xargs)" \
  -d '{"query":"D1110 prophylaxis","limit":3}' \
  | jq '.success' > /dev/null && echo "    ✅ search-guidelines working" || echo "    ❌ search-guidelines failed"

echo "  Testing generate-narrative..."
curl -s -X POST "${BASE_URL}/generate-narrative" \
  -H "Content-Type: application/json" \
  -H "apikey: $(supabase status | grep 'anon key' | cut -d':' -f2 | xargs)" \
  -d '{
    "claim_data": {
      "patient_info": {"name":"Test Patient","dob":"1990-01-01"},
      "procedures": [{"code":"D1110","description":"Prophylaxis","date":"2024-12-26"}]
    },
    "narrative_type": "standard"
  }' \
  | jq '.success' > /dev/null && echo "    ✅ generate-narrative working" || echo "    ❌ generate-narrative failed"

echo "  Testing analyze-claim..."
curl -s -X POST "${BASE_URL}/analyze-claim" \
  -H "Content-Type: application/json" \
  -H "apikey: $(supabase status | grep 'anon key' | cut -d':' -f2 | xargs)" \
  -d '{
    "claim_data": {
      "patient_info": {"name":"Test Patient","dob":"1990-01-01","age":34},
      "procedures": [{"code":"D1110","description":"Prophylaxis","date":"2024-12-26"}]
    },
    "carrier": "Delta Dental",
    "analysis_type": "comprehensive"
  }' \
  | jq '.success' > /dev/null && echo "    ✅ analyze-claim working" || echo "    ❌ analyze-claim failed"

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "📋 Edge Function URLs:"
echo "  🔧 generate-narrative: ${BASE_URL}/generate-narrative"
echo "  🔍 search-guidelines:  ${BASE_URL}/search-guidelines"
echo "  📋 analyze-claim:      ${BASE_URL}/analyze-claim"
echo ""
echo "📊 Supabase Dashboard: https://supabase.com/dashboard/project/${PROJECT_REF}"
echo ""
echo "🔑 Next steps:"
echo "  1. Set OPENAI_API_KEY if not already set:"
echo "     supabase secrets set OPENAI_API_KEY=your_key"
echo "  2. Test the functions with your Custom GPT"
echo "  3. Monitor function logs in the Supabase dashboard"
