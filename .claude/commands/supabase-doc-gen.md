## Supabase DB Documentation

**Command**: `/supabase:doc-gen`

**Variables**: 
- project_id: $ARGUMENTS[0] (required)
- doc_name: $ARGUMENTS[1] (optional, defaults to project name)  
- focus: $ARGUMENTS[2] (optional: schema|rls|functions|all, defaults to all)

**Steps**:
1. Get project details: `supabase:get_project $project_id`
2. List all tables: `supabase:list_tables $project_id`
3. Get security advisors: `supabase:get_advisors $project_id security`
4. Get performance advisors: `supabase:get_advisors $project_id performance`
5. List extensions: `supabase:list_extensions $project_id`
6. List migrations: `supabase:list_migrations $project_id`
7. Generate TypeScript types: `supabase:generate_typescript_types $project_id`
8. Get project URL and anon key for API docs

**Output Structure**:
```
docs/supabase/{project_name}/
├── schema.md          # Tables, columns, relationships
├── security.md        # RLS policies, auth patterns  
├── functions.md       # Database & edge functions
├── api.md            # Endpoints, types, patterns
├── migrations.md     # History & rollback procedures
└── performance.md    # Indexes, optimization tips
```

**Usage**:
```bash
claude /supabase:doc-gen {project-id}
claude /supabase:doc-gen {project-id} "custom-name" "schema"
```
