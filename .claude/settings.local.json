{"permissions": {"allow": ["Bash(supabase projects:*)", "Bash(supabase db dump:*)", "Bash(supabase migration list:*)", "mcp__supabase__get_project", "mcp__supabase__list_tables", "mcp__supabase__get_advisors", "mcp__supabase__list_extensions", "mcp__supabase__list_migrations", "mcp__supabase__generate_typescript_types", "mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "mcp__supabase__execute_sql", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}