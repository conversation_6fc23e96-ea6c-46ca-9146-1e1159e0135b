# Custom GPT Testing Guide

## Overview
This guide provides comprehensive test scenarios to validate the Custom GPT integration with Dental Narrator Edge Functions.

## Pre-Testing Checklist
- [ ] Edge Functions deployed to Supabase
- [ ] Database migrations applied
- [ ] Environment variables set (OPENAI_API_KEY, etc.)
- [ ] Custom GPT created with proper configuration
- [ ] OpenAPI schema imported
- [ ] Authentication configured

## Test Scenarios

### Test 1: Basic Guideline Search
**Objective**: Verify search-guidelines function works correctly

**Test Input**:
```
"Search for coverage guidelines for D1110 prophylaxis with Delta Dental"
```

**Expected Behavior**:
1. GPT should use the search_guidelines action
2. Should return relevant guidelines with similarity scores
3. Should provide clear, formatted response with coverage details

**Success Criteria**:
- [ ] Action called successfully
- [ ] Results returned with proper formatting
- [ ] Similarity scores > 0.3 for relevant results
- [ ] Response includes carrier-specific information

### Test 2: Complex Guideline Search
**Objective**: Test advanced search capabilities

**Test Input**:
```
"What are the frequency limitations for periodontal maintenance (D4910) across different carriers?"
```

**Expected Behavior**:
1. Should search for D4910 guidelines
2. Should identify frequency-related content
3. Should compare different carriers if available

**Success Criteria**:
- [ ] Multiple guidelines returned
- [ ] Frequency information extracted
- [ ] Carrier comparisons provided

### Test 3: Narrative Generation - Standard
**Objective**: Test basic narrative generation

**Test Input**:
```
"Generate a narrative for a 45-year-old patient named John Smith (DOB: 1979-03-15, Member ID: DD123456) who received:
- D1110 Prophylaxis on 2024-12-20
- D0150 Comprehensive exam on 2024-12-20
Diagnosis: Gingivitis, chronic
Carrier: Delta Dental"
```

**Expected Behavior**:
1. Should use generate_narrative action
2. Should create professional narrative
3. Should include medical necessity justification
4. Should reference relevant guidelines

**Success Criteria**:
- [ ] Narrative generated successfully
- [ ] Professional medical terminology used
- [ ] Patient information correctly incorporated
- [ ] Supporting guidelines referenced
- [ ] Confidence score provided

### Test 4: Narrative Generation - Appeal
**Objective**: Test appeal narrative generation

**Test Input**:
```
"Generate an appeal narrative for a crown procedure (D2750) that was initially denied. Patient: Jane Doe, DOB: 1985-07-22, Tooth #14, performed on 2024-12-15. The tooth had extensive decay and previous large filling failure."
```

**Expected Behavior**:
1. Should generate appeal-specific narrative
2. Should address denial reasons
3. Should emphasize medical necessity
4. Should include appeal-specific recommendations

**Success Criteria**:
- [ ] Appeal narrative format used
- [ ] Medical necessity strongly emphasized
- [ ] Specific clinical details included
- [ ] Appeal recommendations provided

### Test 5: Claim Analysis - Comprehensive
**Objective**: Test comprehensive claim analysis

**Test Input**:
```
"Analyze this claim for approval likelihood:
Patient: Robert Johnson, Age: 52, DOB: 1972-05-10
Procedures:
- D2150 Amalgam filling, tooth #19, 2024-12-18
- D0220 Periapical X-ray, tooth #19, 2024-12-18
Diagnosis: Dental caries, chronic
Carrier: Blue Cross Blue Shield
Medical History: Diabetes Type 2, controlled"
```

**Expected Behavior**:
1. Should use analyze_claim action
2. Should provide comprehensive analysis
3. Should identify risk factors
4. Should predict approval likelihood
5. Should provide recommendations

**Success Criteria**:
- [ ] Analysis completed successfully
- [ ] Risk factors identified
- [ ] Approval likelihood predicted
- [ ] Specific recommendations provided
- [ ] Carrier information verified

### Test 6: Error Handling
**Objective**: Test error handling and recovery

**Test Input**:
```
"Generate a narrative for a patient with invalid procedure code XYZ123"
```

**Expected Behavior**:
1. Should handle invalid procedure code gracefully
2. Should provide helpful error message
3. Should suggest corrections or alternatives

**Success Criteria**:
- [ ] Error handled gracefully
- [ ] Helpful error message provided
- [ ] User guided to correct input

### Test 7: Multi-Step Workflow
**Objective**: Test complex multi-step interactions

**Test Input**:
```
"I need help with a complex case. First, search for guidelines on D7210 surgical extraction. Then analyze a claim for this procedure for a 35-year-old patient with impacted wisdom tooth. Finally, generate a predetermination narrative."
```

**Expected Behavior**:
1. Should break down into multiple steps
2. Should use multiple actions in sequence
3. Should maintain context between steps
4. Should provide comprehensive final result

**Success Criteria**:
- [ ] Multiple actions used correctly
- [ ] Context maintained throughout
- [ ] Comprehensive final response
- [ ] All steps completed successfully

### Test 8: Carrier-Specific Queries
**Objective**: Test carrier-specific functionality

**Test Input**:
```
"Compare coverage for D4341 periodontal scaling between Aetna and Cigna"
```

**Expected Behavior**:
1. Should search guidelines for both carriers
2. Should compare coverage differences
3. Should highlight key variations

**Success Criteria**:
- [ ] Both carriers searched
- [ ] Differences identified
- [ ] Clear comparison provided

## Performance Testing

### Response Time Expectations
- **Search Guidelines**: < 5 seconds
- **Generate Narrative**: < 15 seconds (due to AI processing)
- **Analyze Claim**: < 10 seconds

### Load Testing
Test with multiple concurrent requests to ensure stability.

## Integration Testing Checklist

### Authentication
- [ ] API key authentication works
- [ ] Proper headers sent with requests
- [ ] Error handling for invalid authentication

### Data Validation
- [ ] Request validation working correctly
- [ ] Proper error messages for invalid data
- [ ] Required fields enforced

### Response Formatting
- [ ] Responses properly formatted as JSON
- [ ] Success/error status correctly indicated
- [ ] Metadata included in responses

### CORS Handling
- [ ] CORS headers properly set
- [ ] Preflight requests handled
- [ ] Cross-origin requests work

## Troubleshooting Common Issues

### Issue: "Action failed to execute"
**Possible Causes**:
- Invalid API key
- Network connectivity issues
- Edge function not deployed

**Solutions**:
1. Verify API key in Custom GPT settings
2. Check Supabase Edge Functions status
3. Review function logs in Supabase dashboard

### Issue: "Validation error"
**Possible Causes**:
- Incorrect request format
- Missing required fields
- Invalid data types

**Solutions**:
1. Check OpenAPI schema compliance
2. Verify all required fields provided
3. Validate data types match schema

### Issue: "Timeout error"
**Possible Causes**:
- Complex AI processing taking too long
- Database query performance issues
- External API (OpenAI) delays

**Solutions**:
1. Optimize database queries
2. Implement request timeouts
3. Add retry logic for external APIs

## Success Metrics

### Functional Metrics
- [ ] 100% of test scenarios pass
- [ ] All actions execute successfully
- [ ] Error handling works correctly
- [ ] Response times within acceptable limits

### Quality Metrics
- [ ] Generated narratives are professional and accurate
- [ ] Guideline searches return relevant results
- [ ] Claim analyses provide actionable insights
- [ ] User experience is smooth and intuitive

## Post-Testing Actions

### If Tests Pass
1. Document successful test results
2. Create user training materials
3. Plan production rollout
4. Set up monitoring and alerts

### If Tests Fail
1. Document specific failures
2. Identify root causes
3. Implement fixes
4. Re-run failed tests
5. Update documentation as needed

## Monitoring and Maintenance

### Ongoing Monitoring
- Monitor Edge Function performance
- Track API usage and costs
- Review user feedback
- Monitor error rates

### Regular Maintenance
- Update guidelines database
- Refresh AI models as needed
- Update Custom GPT instructions
- Review and optimize performance

## Contact Information
For technical support or questions:
- Email: <EMAIL>
- Supabase Dashboard: https://supabase.com/dashboard/project/ymivwfdmeymosgvgoibb
- Edge Functions Logs: Available in Supabase dashboard
