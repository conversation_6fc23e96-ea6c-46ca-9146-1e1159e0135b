{"openapi": "3.1.0", "info": {"title": "Dental Narrator Edge Functions API", "description": "AI-powered dental insurance claim processing and narrative generation API. Provides comprehensive tools for searching guidelines, generating narratives, and analyzing claims for optimal insurance approval rates.", "version": "1.0.0", "contact": {"name": "Dental Narrator Support", "email": "<EMAIL>"}}, "servers": [{"url": "https://ymivwfdmeymosgvgoibb.supabase.co/functions/v1", "description": "Production Supabase Edge Functions"}], "security": [{"BearerAuth": []}], "paths": {"/search-guidelines": {"post": {"summary": "Search Dental Insurance Guidelines", "description": "Search through 2,006+ dental insurance guidelines using AI-powered vector similarity search. Find relevant coverage information, requirements, and limitations for specific procedures or carriers.", "operationId": "searchGuidelines", "tags": ["Guidelines"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "Search query for guidelines (e.g., 'D1110 prophylaxis coverage', 'root canal requirements')", "example": "D1110 prophylaxis cleaning coverage requirements"}, "carrier": {"type": "string", "description": "Filter by specific insurance carrier", "example": "Delta Dental"}, "category": {"type": "string", "description": "Filter by procedure category", "example": "preventive"}, "limit": {"type": "integer", "minimum": 1, "maximum": 50, "default": 5, "description": "Maximum number of results to return"}}}}}}, "responses": {"200": {"description": "Guidelines search results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchGuidelinesResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/generate-narrative": {"post": {"summary": "Generate Insurance Claim Narrative", "description": "Generate professional, AI-powered dental insurance claim narratives that maximize approval chances. Includes medical necessity justification and carrier-specific formatting.", "operationId": "generateNarrative", "tags": ["Narratives"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["claim_data"], "properties": {"claim_data": {"$ref": "#/components/schemas/ClaimData"}, "carrier": {"type": "string", "description": "Insurance carrier name for carrier-specific formatting", "example": "Delta Dental"}, "narrative_type": {"type": "string", "enum": ["standard", "appeal", "predetermination"], "default": "standard", "description": "Type of narrative to generate"}}}}}}, "responses": {"200": {"description": "Generated narrative with supporting information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateNarrativeResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/analyze-claim": {"post": {"summary": "Analyze <PERSON><PERSON><PERSON> for Approval Likelihood", "description": "Comprehensive analysis of dental insurance claims including coverage verification, medical necessity assessment, frequency limitations, and approval likelihood prediction.", "operationId": "analyzeClaim", "tags": ["Analysis"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["claim_data", "carrier"], "properties": {"claim_data": {"$ref": "#/components/schemas/ClaimData"}, "carrier": {"type": "string", "description": "Insurance carrier name", "example": "Blue Cross Blue Shield"}, "analysis_type": {"type": "string", "enum": ["coverage", "medical_necessity", "frequency", "comprehensive"], "default": "comprehensive", "description": "Type of analysis to perform"}}}}}}, "responses": {"200": {"description": "Comprehensive claim analysis results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyzeClaimResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Carrier not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/documentation-requirements": {"post": {"summary": "Get Documentation Requirements", "description": "Retrieve comprehensive documentation requirements for specific procedures and carriers, including submission guidelines and compliance checklists.", "operationId": "getDocumentationRequirements", "tags": ["Documentation"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["procedure_codes", "carrier"], "properties": {"procedure_codes": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of CDT procedure codes", "example": ["D1110", "D0150"]}, "carrier": {"type": "string", "description": "Insurance carrier name", "example": "Delta Dental"}, "patient_age": {"type": "integer", "minimum": 0, "maximum": 150, "description": "Patient age for age-specific requirements"}, "diagnosis": {"type": "string", "description": "Clinical diagnosis", "example": "Gingivitis, chronic"}, "request_type": {"type": "string", "enum": ["predetermination", "claim", "appeal"], "default": "claim", "description": "Type of request"}}}}}}, "responses": {"200": {"description": "Documentation requirements and guidelines", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentationRequirementsResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Carrier not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/appeal-assistance": {"post": {"summary": "Get Appeal Assistance", "description": "Comprehensive appeal assistance for denied dental claims including denial analysis, appeal strategy, letter templates, and success probability assessment.", "operationId": "getAppealAssistance", "tags": ["Appeals"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["original_claim", "carrier", "patient_info"], "properties": {"original_claim": {"type": "object", "required": ["procedures", "denial_reason", "denial_date"], "properties": {"procedures": {"type": "array", "items": {"type": "object", "required": ["code", "date"], "properties": {"code": {"type": "string", "description": "CDT procedure code"}, "description": {"type": "string", "description": "Procedure description"}, "date": {"type": "string", "format": "date", "description": "Procedure date"}, "denied_amount": {"type": "number", "minimum": 0, "description": "Amount denied"}}}}, "denial_reason": {"type": "string", "description": "Reason for denial", "example": "Not medically necessary"}, "denial_date": {"type": "string", "format": "date", "description": "Date of denial"}}}, "carrier": {"type": "string", "description": "Insurance carrier name", "example": "<PERSON><PERSON><PERSON>"}, "patient_info": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Patient name"}, "member_id": {"type": "string", "description": "Insurance member ID"}, "age": {"type": "integer", "minimum": 0, "maximum": 150, "description": "Patient age"}}}, "additional_documentation": {"type": "array", "items": {"type": "string"}, "description": "Additional documentation available"}, "appeal_level": {"type": "string", "enum": ["first", "second", "external"], "default": "first", "description": "Level of appeal"}}}}}}, "responses": {"200": {"description": "Comprehensive appeal assistance package", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppealAssistanceResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Carrier not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/predetermination-analysis": {"post": {"summary": "Analyze Treatment Plan for Predetermination", "description": "Comprehensive analysis of treatment plans for predetermination submission including coverage predictions, risk assessment, and optimization recommendations.", "operationId": "analyzePredetermination", "tags": ["Predetermination"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["treatment_plan", "patient_info", "carrier"], "properties": {"treatment_plan": {"type": "object", "required": ["procedures", "diagnosis"], "properties": {"procedures": {"type": "array", "items": {"type": "object", "required": ["code", "planned_date"], "properties": {"code": {"type": "string", "description": "CDT procedure code"}, "description": {"type": "string", "description": "Procedure description"}, "planned_date": {"type": "string", "format": "date", "description": "Planned treatment date"}, "tooth": {"type": "string", "description": "Tooth number"}, "surfaces": {"type": "array", "items": {"type": "string"}, "description": "Tooth surfaces"}, "estimated_fee": {"type": "number", "minimum": 0, "description": "Estimated procedure fee"}, "priority": {"type": "string", "enum": ["urgent", "high", "medium", "low"], "default": "medium", "description": "Treatment priority"}}}}, "diagnosis": {"type": "string", "description": "Clinical diagnosis"}, "treatment_sequence": {"type": "array", "items": {"type": "string"}, "description": "Planned treatment sequence"}, "alternative_treatments": {"type": "array", "items": {"type": "string"}, "description": "Alternative treatment options"}}}, "patient_info": {"type": "object", "required": ["name", "dob"], "properties": {"name": {"type": "string", "description": "Patient name"}, "dob": {"type": "string", "format": "date", "description": "Date of birth"}, "member_id": {"type": "string", "description": "Insurance member ID"}, "medical_history": {"type": "string", "description": "Relevant medical history"}, "current_oral_health": {"type": "string", "description": "Current oral health status"}}}, "carrier": {"type": "string", "description": "Insurance carrier name", "example": "<PERSON><PERSON><PERSON>"}, "analysis_depth": {"type": "string", "enum": ["basic", "comprehensive", "detailed"], "default": "comprehensive", "description": "Depth of analysis to perform"}}}}}}, "responses": {"200": {"description": "Comprehensive predetermination analysis", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PredeterminationAnalysisResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Carrier not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Supabase JWT token for authentication. Use your Supabase anon key as the bearer token."}}, "schemas": {"ClaimData": {"type": "object", "required": ["patient_info", "procedures"], "properties": {"patient_info": {"type": "object", "required": ["name", "dob"], "properties": {"name": {"type": "string", "description": "<PERSON><PERSON>'s full name", "example": "<PERSON>"}, "dob": {"type": "string", "format": "date", "description": "<PERSON><PERSON>'s date of birth (YYYY-MM-DD)", "example": "1985-06-15"}, "member_id": {"type": "string", "description": "Insurance member ID", "example": "DD123456789"}, "age": {"type": "integer", "minimum": 0, "maximum": 150, "description": "Patient's age"}}}, "procedures": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["code", "description", "date"], "properties": {"code": {"type": "string", "description": "CDT procedure code", "example": "D1110"}, "description": {"type": "string", "description": "Procedure description", "example": "Prophylaxis - adult"}, "date": {"type": "string", "format": "date", "description": "Procedure date (YYYY-MM-DD)", "example": "2024-12-20"}, "tooth": {"type": "string", "description": "Tooth number if applicable", "example": "14"}, "surfaces": {"type": "array", "items": {"type": "string"}, "description": "Tooth surfaces involved", "example": ["M", "O"]}, "fee": {"type": "number", "minimum": 0, "description": "Procedure fee"}}}}, "diagnosis": {"type": "string", "description": "Clinical diagnosis", "example": "Gingivitis, chronic"}, "medical_history": {"type": "string", "description": "Relevant medical history", "example": "No significant medical history"}, "treatment_plan": {"type": "string", "description": "Treatment plan description", "example": "Routine prophylaxis and oral hygiene instruction"}}}, "SearchGuidelinesResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Guideline ID"}, "title": {"type": "string", "description": "Guideline title"}, "category": {"type": "string", "description": "Procedure category"}, "carrier": {"type": "string", "description": "Insurance carrier"}, "similarity_score": {"type": "number", "description": "Relevance score (0-1)"}, "content": {"type": "string", "description": "Guideline content"}}}}, "total_found": {"type": "integer", "description": "Total number of results found"}, "search_metadata": {"type": "object", "description": "Search metadata and insights"}}}, "metadata": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "processing_time_ms": {"type": "integer"}}}}}, "GenerateNarrativeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"narrative": {"type": "string", "description": "Generated insurance narrative"}, "supporting_guidelines": {"type": "array", "items": {"type": "object", "description": "Guidelines used to support the narrative"}}, "recommendations": {"type": "array", "items": {"type": "string"}, "description": "Recommendations for improving approval chances"}, "confidence_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confidence score for the generated narrative"}}}, "metadata": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "processing_time_ms": {"type": "integer"}, "guidelines_found": {"type": "integer"}, "narrative_type": {"type": "string"}}}}}, "AnalyzeClaimResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"claim_analysis": {"type": "object", "properties": {"overall_assessment": {"type": "object", "description": "Overall claim assessment summary"}, "procedure_analysis": {"type": "array", "items": {"type": "object"}, "description": "Individual procedure analysis"}, "risk_factors": {"type": "array", "items": {"type": "string"}, "description": "Identified risk factors"}, "recommendations": {"type": "array", "items": {"type": "string"}, "description": "Recommendations for improving approval"}, "approval_likelihood": {"type": "string", "enum": ["high", "medium", "low"], "description": "Predicted approval likelihood"}}}, "carrier_info": {"type": "object", "properties": {"name": {"type": "string"}, "payer_id": {"type": "string"}}}, "procedures_analyzed": {"type": "array", "items": {"type": "object"}, "description": "Detailed procedure information"}}}, "metadata": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "processing_time_ms": {"type": "integer"}}}}}, "DocumentationRequirementsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"carrier_info": {"type": "object", "properties": {"name": {"type": "string"}, "payer_id": {"type": "string"}}}, "request_type": {"type": "string"}, "procedure_requirements": {"type": "array", "items": {"type": "object"}}, "comprehensive_requirements": {"type": "object", "properties": {"universal_requirements": {"type": "array", "items": {"type": "string"}}, "carrier_requirements": {"type": "array", "items": {"type": "string"}}, "submission_format": {"type": "array", "items": {"type": "string"}}}}, "compliance_checklist": {"type": "array", "items": {"type": "string"}}}}}}, "AppealAssistanceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"denial_analysis": {"type": "object", "properties": {"denial_category": {"type": "string"}, "specific_issues": {"type": "array", "items": {"type": "string"}}, "appeal_grounds": {"type": "array", "items": {"type": "string"}}, "strength_assessment": {"type": "string", "enum": ["strong", "moderate", "weak"]}}}, "appeal_strategy": {"type": "object", "properties": {"primary_approach": {"type": "string"}, "key_arguments": {"type": "array", "items": {"type": "string"}}, "supporting_evidence": {"type": "array", "items": {"type": "string"}}}}, "appeal_letter_template": {"type": "string", "description": "Template for appeal letter"}, "success_probability": {"type": "object", "properties": {"percentage": {"type": "integer", "minimum": 0, "maximum": 100}, "confidence_level": {"type": "string", "enum": ["high", "moderate", "low"]}}}}}}}, "PredeterminationAnalysisResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"predetermination_analysis": {"type": "object", "properties": {"overall_assessment": {"type": "object", "properties": {"total_procedures": {"type": "integer"}, "estimated_approval_rate": {"type": "number", "minimum": 0, "maximum": 100}, "total_estimated_cost": {"type": "number", "minimum": 0}, "approval_likelihood": {"type": "string", "enum": ["high", "medium", "low"]}}}, "procedure_analysis": {"type": "array", "items": {"type": "object", "properties": {"procedure_code": {"type": "string"}, "coverage_likelihood": {"type": "string", "enum": ["covered", "questionable", "unknown"]}, "risk_level": {"type": "string", "enum": ["low", "medium", "high"]}, "coverage_percentage": {"type": "number", "minimum": 0, "maximum": 100}}}}, "coverage_predictions": {"type": "object", "properties": {"total_estimated_cost": {"type": "number"}, "total_estimated_coverage": {"type": "number"}, "patient_responsibility": {"type": "number"}}}, "optimization_recommendations": {"type": "array", "items": {"type": "string"}}}}, "submission_recommendations": {"type": "array", "items": {"type": "string"}}, "estimated_timeline": {"type": "object", "properties": {"review_period": {"type": "string"}, "total_timeline": {"type": "string"}}}}}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "Error code"}, "message": {"type": "string", "description": "Human-readable error message"}, "metadata": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}}}}}}}, "tags": [{"name": "Guidelines", "description": "Search and retrieve dental insurance guidelines"}, {"name": "Narratives", "description": "Generate AI-powered insurance claim narratives"}, {"name": "Analysis", "description": "Analyze claims for approval likelihood and compliance"}, {"name": "Documentation", "description": "Get documentation requirements and submission guidelines"}, {"name": "Appeals", "description": "Comprehensive appeal assistance for denied claims"}, {"name": "Predetermination", "description": "Analyze treatment plans for predetermination submission"}]}