# Performance Documentation
**Project**: dental-narrator-beta  
**Generated**: 2025-06-26

## Performance Status Overview

⚠️ **PERFORMANCE ISSUES DETECTED**

The database has several performance optimization opportunities:
- **11 unindexed foreign keys** causing potential query slowdowns
- **6 unused indexes** consuming unnecessary storage
- Missing indexes on frequently queried columns

## Foreign Key Performance Issues

### ❌ Unindexed Foreign Keys (11 Issues)

Foreign keys without covering indexes can cause significant performance degradation, especially for JOIN operations and cascading deletes.

**Critical Issues:**

1. **`appeal_procedures.carrier_id`**
   - Foreign key: `appeal_procedures_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow JOINs when querying appeals by carrier

2. **`carrier_aliases.carrier_id`**
   - Foreign key: `carrier_aliases_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow carrier name resolution

3. **`carrier_procedure_requirements.procedure_id`**
   - Foreign key: `carrier_procedure_requirements_procedure_id_fkey` → `procedures.id`
   - Impact: Slow requirement lookups by procedure

4. **`credentialing_requirements.carrier_id`**
   - Foreign key: `credentialing_requirements_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow credentialing queries by carrier

5. **`credentialing_requirements.network_id`**
   - Foreign key: `credentialing_requirements_network_id_fkey` → `insurance_networks.id`
   - Impact: Slow credentialing queries by network

6. **`documentation_requirements.carrier_id`**
   - Foreign key: `documentation_requirements_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow documentation requirement lookups

7. **`insurance_plans.carrier_id`**
   - Foreign key: `insurance_plans_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow plan queries by carrier

8. **`medicare_advantage_plans.carrier_id`**
   - Foreign key: `medicare_advantage_plans_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow Medicare plan lookups

9. **`network_carrier_relationships.carrier_id`**
   - Foreign key: `network_carrier_relationships_carrier_id_fkey` → `insurance_carriers.id`
   - Impact: Slow network relationship queries

10. **`processing_caveats.carrier_id`**
    - Foreign key: `processing_caveats_carrier_id_fkey` → `insurance_carriers.id`
    - Impact: Slow caveat lookups by carrier

11. **`processing_caveats.network_id`**
    - Foreign key: `processing_caveats_network_id_fkey` → `insurance_networks.id`
    - Impact: Slow caveat lookups by network

### 🔧 **Immediate Remediation**

Create indexes for all unindexed foreign keys:

```sql
-- Appeal procedures
CREATE INDEX CONCURRENTLY idx_appeal_procedures_carrier_id 
ON appeal_procedures(carrier_id);

-- Carrier aliases  
CREATE INDEX CONCURRENTLY idx_carrier_aliases_carrier_id 
ON carrier_aliases(carrier_id);

-- Carrier procedure requirements
CREATE INDEX CONCURRENTLY idx_carrier_procedure_requirements_procedure_id 
ON carrier_procedure_requirements(procedure_id);

-- Credentialing requirements
CREATE INDEX CONCURRENTLY idx_credentialing_requirements_carrier_id 
ON credentialing_requirements(carrier_id);

CREATE INDEX CONCURRENTLY idx_credentialing_requirements_network_id 
ON credentialing_requirements(network_id);

-- Documentation requirements
CREATE INDEX CONCURRENTLY idx_documentation_requirements_carrier_id 
ON documentation_requirements(carrier_id);

-- Insurance plans
CREATE INDEX CONCURRENTLY idx_insurance_plans_carrier_id 
ON insurance_plans(carrier_id);

-- Medicare advantage plans
CREATE INDEX CONCURRENTLY idx_medicare_advantage_plans_carrier_id 
ON medicare_advantage_plans(carrier_id);

-- Network carrier relationships
CREATE INDEX CONCURRENTLY idx_network_carrier_relationships_carrier_id 
ON network_carrier_relationships(carrier_id);

-- Processing caveats
CREATE INDEX CONCURRENTLY idx_processing_caveats_carrier_id 
ON processing_caveats(carrier_id);

CREATE INDEX CONCURRENTLY idx_processing_caveats_network_id 
ON processing_caveats(network_id);
```

**Note**: Use `CONCURRENTLY` to avoid locking tables during index creation.

## Unused Index Analysis

### 🗑️ Unused Indexes (6 Issues)

These indexes consume storage space but haven't been used for queries:

1. **`idx_json_uploads_file_name`** on `json_uploads.file_name`
   - **Storage Impact**: Low
   - **Recommendation**: Remove if file name searches aren't needed

2. **`idx_glossary_terms_term`** on `glossary_terms.term`
   - **Storage Impact**: Medium
   - **Recommendation**: Keep if glossary search is planned

3. **`idx_glossary_terms_letter`** on `glossary_terms.letter`
   - **Storage Impact**: Low
   - **Recommendation**: Remove, unlikely to be used for queries

4. **`idx_embeddings_metadata_gin`** on `embeddings.metadata`
   - **Storage Impact**: High
   - **Recommendation**: Keep, likely needed for metadata filtering

5. **`idx_embeddings_vector_cosine`** on `embeddings.embedding`
   - **Storage Impact**: Very High
   - **Recommendation**: Keep, essential for vector similarity search

6. **`idx_document_chunks_embedding`** on `document_chunks.embedding`
   - **Storage Impact**: High
   - **Recommendation**: Keep, needed for chunk-based vector search

7. **`idx_ingested_documents_type`** on `ingested_documents.document_type`
   - **Storage Impact**: Low
   - **Recommendation**: Keep, likely needed for document filtering

8. **`idx_ingested_documents_insurer`** on `ingested_documents.insurer`
   - **Storage Impact**: Medium
   - **Recommendation**: Keep, needed for insurer-based filtering

### 🔧 **Index Cleanup Recommendations**

**Safe to Remove:**
```sql
-- Remove unused indexes that are unlikely to be needed
DROP INDEX CONCURRENTLY idx_json_uploads_file_name;
DROP INDEX CONCURRENTLY idx_glossary_terms_letter;
```

**Monitor and Evaluate:**
- Keep vector and metadata indexes as they're essential for RAG functionality
- Monitor usage over next 30 days before deciding on glossary and document indexes

## Missing Indexes Analysis

### 📈 **Recommended Additional Indexes**

Based on the application's RAG functionality, these indexes would improve performance:

```sql
-- Guidelines query optimization
CREATE INDEX CONCURRENTLY idx_guidelines_carrier_category 
ON guidelines(carrier_id, category) WHERE expiration_date IS NULL OR expiration_date > CURRENT_DATE;

CREATE INDEX CONCURRENTLY idx_guidelines_effective_date 
ON guidelines(effective_date) WHERE effective_date <= CURRENT_DATE;

-- Procedure code lookups
CREATE INDEX CONCURRENTLY idx_procedures_code_hash 
ON procedures USING hash(procedure_code);

-- Search optimization
CREATE INDEX CONCURRENTLY idx_search_logs_created_at 
ON search_logs(created_at DESC);

-- Carrier name searches
CREATE INDEX CONCURRENTLY idx_insurance_carriers_name_gin 
ON insurance_carriers USING gin(carrier_name gin_trgm_ops);

-- Document content search
CREATE INDEX CONCURRENTLY idx_ingested_documents_insurer_type 
ON ingested_documents(insurer, document_type, processing_status);
```

## Vector Search Optimization

### 🎯 **HNSW Index Configuration**

Current vector indexes use default parameters. Optimize for your workload:

```sql
-- Optimize embeddings vector index
DROP INDEX CONCURRENTLY idx_embeddings_vector_cosine;
CREATE INDEX CONCURRENTLY idx_embeddings_vector_hnsw 
ON embeddings USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- Optimize document chunks vector index  
DROP INDEX CONCURRENTLY idx_document_chunks_embedding;
CREATE INDEX CONCURRENTLY idx_document_chunks_embedding_hnsw 
ON document_chunks USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);
```

**HNSW Parameters Explained:**
- `m = 16`: Number of bi-directional links (default 16, good for most cases)
- `ef_construction = 64`: Size of dynamic candidate list (higher = better recall, slower build)

### 🔧 **Vector Search Function Optimization**

Current search functions can be optimized:

```sql
-- Add index hints and optimize search_guidelines_vector
CREATE OR REPLACE FUNCTION search_guidelines_vector(
    query_embedding vector(1536),
    content_type_filter text DEFAULT NULL,
    carrier_id_filter bigint DEFAULT NULL,
    category_filter text DEFAULT NULL,
    similarity_threshold float DEFAULT 0.7,
    max_results integer DEFAULT 10
)
RETURNS TABLE(
    id bigint,
    content_id bigint,
    similarity_score float,
    metadata jsonb,
    created_at timestamptz
)
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.content_id,
        (1 - (e.embedding <=> query_embedding))::float as similarity_score,
        e.metadata,
        e.created_at
    FROM embeddings e
    WHERE 
        (content_type_filter IS NULL OR e.content_type = content_type_filter)
        AND (carrier_id_filter IS NULL OR (e.metadata->>'carrier_id')::bigint = carrier_id_filter)
        AND (category_filter IS NULL OR (e.metadata->>'category') = category_filter)
        AND (1 - (e.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT max_results;
END;
$$;
```

## Query Performance Monitoring

### 📊 **Performance Monitoring Setup**

Enable query statistics:

```sql
-- Enable query statistics (if not already enabled)
-- This should be done at database/server level:
-- shared_preload_libraries = 'pg_stat_statements'

-- Create view for slow queries
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_ratio
FROM pg_stat_statements
WHERE mean_time > 100  -- Queries taking more than 100ms on average
ORDER BY mean_time DESC;
```

### 🔍 **Common Performance Queries**

```sql
-- Check index usage
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public'
ORDER BY tablename, attname;

-- Monitor vector search performance
SELECT 
    query_text,
    AVG(execution_time) as avg_execution_time,
    COUNT(*) as query_count
FROM query_logs
WHERE query_type = 'vector_search'
  AND timestamp > NOW() - INTERVAL '24 hours'
GROUP BY query_text
ORDER BY avg_execution_time DESC;

-- Check table bloat
SELECT 
    schemaname,
    tablename,
    n_dead_tup,
    n_live_tup,
    round(n_dead_tup::numeric / NULLIF(n_live_tup + n_dead_tup, 0) * 100, 2) as dead_ratio
FROM pg_stat_user_tables
WHERE n_dead_tup > 0
ORDER BY dead_ratio DESC;
```

## Connection and Resource Optimization

### 🔧 **Database Configuration**

Recommended settings for RAG workload:

```sql
-- Connection settings
max_connections = 100
shared_buffers = '256MB'
effective_cache_size = '1GB'
maintenance_work_mem = '64MB'
checkpoint_completion_target = 0.9
wal_buffers = '16MB'
default_statistics_target = 100

-- Vector-specific settings
max_parallel_workers_per_gather = 2
max_parallel_workers = 4
```

### 📈 **Scaling Recommendations**

**Current Workload Assumptions:**
- Primary use: Vector similarity search
- Secondary use: Carrier/procedure lookups
- Read-heavy workload with periodic data updates

**Scaling Strategies:**

1. **Read Replicas**: For read-heavy analytics queries
2. **Connection Pooling**: Use PgBouncer for connection management
3. **Caching**: Implement Redis for frequently accessed reference data
4. **Partitioning**: Consider partitioning large tables (embeddings, logs) by date

## Performance Testing

### 🧪 **Benchmark Queries**

Test these queries before/after optimizations:

```sql
-- Vector search benchmark
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM search_guidelines_vector(
    '[random 1536-dimension vector]',
    'guideline',
    1,
    'preventive',
    0.7,
    10
);

-- Carrier lookup benchmark
EXPLAIN (ANALYZE, BUFFERS)
SELECT c.*, ca.alias_name, cp.documentation_required
FROM insurance_carriers c
LEFT JOIN carrier_aliases ca ON c.id = ca.carrier_id
LEFT JOIN carrier_procedure_requirements cp ON c.id = cp.carrier_id
WHERE c.carrier_name ILIKE '%delta%';

-- Complex join benchmark
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    c.carrier_name,
    p.procedure_code,
    g.title,
    COUNT(sl.id) as search_count
FROM insurance_carriers c
JOIN guidelines g ON c.id = g.carrier_id
JOIN procedures p ON g.metadata->>'procedure_code' = p.procedure_code
LEFT JOIN search_logs sl ON sl.query_text ILIKE '%' || c.carrier_name || '%'
WHERE g.effective_date <= CURRENT_DATE
  AND (g.expiration_date IS NULL OR g.expiration_date > CURRENT_DATE)
GROUP BY c.carrier_name, p.procedure_code, g.title
ORDER BY search_count DESC;
```

## Maintenance Schedule

### 🗓️ **Regular Maintenance Tasks**

**Daily:**
- Monitor slow query log
- Check connection pool usage

**Weekly:**
- Analyze index usage statistics
- Review and cleanup unused indexes
- Update table statistics: `ANALYZE;`

**Monthly:**
- Full VACUUM on large tables
- Review and optimize vector index parameters
- Performance testing with realistic workloads

**Quarterly:**
- Review partitioning strategy for large tables
- Evaluate need for read replicas
- Comprehensive performance audit