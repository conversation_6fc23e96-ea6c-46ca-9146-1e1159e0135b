# Migrations Documentation
**Project**: dental-narrator-beta  
**Generated**: 2025-06-26

## Migration History

The dental-narrator-beta database has evolved through several migration phases, transforming from a basic insurance data structure to a comprehensive RAG-enabled system with vector embeddings and advanced search capabilities.

## Migration Timeline

### 1. `20240325000000_dental_insurance_tables`
**Date**: March 25, 2024  
**Purpose**: Initial database schema for dental insurance system

**Tables Created:**
- `insurance_carriers` - Primary carrier information
- `insurance_networks` - Network details
- `procedures` - CDT procedure codes
- `appeal_procedures` - Appeal processes
- `documentation_requirements` - Required documentation
- `credentialing_requirements` - Provider credentials
- `processing_caveats` - Known processing issues
- `carrier_aliases` - Alternative carrier names
- `carrier_procedure_requirements` - Procedure-specific requirements
- `insurance_plans` - Plan information
- `medicare_advantage_plans` - Medicare-specific plans
- `network_carrier_relationships` - Network associations

**Key Features:**
- Basic insurance data model
- Foreign key relationships
- JSONB fields for flexible data storage
- Audit timestamps (created_at, updated_at)

**SQL Preview:**
```sql
-- Core carrier table
CREATE TABLE insurance_carriers (
    id BIGSERIAL PRIMARY KEY,
    carrier_name TEXT NOT NULL,
    carrier_type TEXT,
    payer_id TEXT,
    claims_address TEXT,
    phone_number TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Procedure definitions
CREATE TABLE procedures (
    id BIGSERIAL PRIMARY KEY,
    procedure_code TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. `20240325010000_setup_json_uploads`
**Date**: March 25, 2024  
**Purpose**: Enable JSON document uploads for data ingestion

**Tables Created:**
- `json_uploads` - Track uploaded JSON files

**Features:**
- File name tracking
- JSONB content storage
- Upload timestamp tracking

**Use Case:**
Support for bulk data imports from insurance carriers via JSON files.

### 3. `20240325020000_create_json_uploads`
**Date**: March 25, 2024  
**Purpose**: Finalize JSON upload functionality

**Enhancements:**
- Additional indexes for JSON queries
- Performance optimizations
- Data validation constraints

### 4. `20250622013224_add_vector_tables_for_rag`
**Date**: June 22, 2025  
**Purpose**: Add RAG (Retrieval-Augmented Generation) capabilities

**Major Addition**: Vector embeddings and search infrastructure

**Tables Created:**
- `embeddings` - Vector embeddings for semantic search
- `documents` - Document storage and management
- `search_logs` - Search query analytics
- `query_logs` - Database query performance tracking
- `search_requests` - API request tracking

**Vector Functions Created:**
- `search_guidelines_vector()` - Vector similarity search
- `search_guidelines_hybrid()` - Hybrid search with recency

**Extensions Added:**
- `vector` - PostgreSQL vector extension for embeddings

**SQL Preview:**
```sql
-- Vector embeddings table
CREATE TABLE embeddings (
    id BIGSERIAL PRIMARY KEY,
    content_type VARCHAR(50) NOT NULL,
    content_id BIGINT NOT NULL,
    embedding VECTOR(1536),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Vector similarity search function
CREATE OR REPLACE FUNCTION search_guidelines_vector(
    query_embedding VECTOR(1536),
    content_type_filter TEXT DEFAULT NULL,
    carrier_id_filter BIGINT DEFAULT NULL,
    category_filter TEXT DEFAULT NULL,
    similarity_threshold FLOAT DEFAULT 0.7,
    max_results INTEGER DEFAULT 10
)
RETURNS TABLE(
    id BIGINT,
    content_id BIGINT,
    similarity_score FLOAT,
    metadata JSONB,
    created_at TIMESTAMPTZ
);
```

### 5. `20250622014512_comprehensive_data_migration_part1_fixed`
**Date**: June 22, 2025  
**Purpose**: Comprehensive data migration and schema improvements

**Enhancements:**
- Data integrity improvements
- Performance optimizations
- Additional indexes for common queries
- Constraint additions for data validation

**Key Changes:**
- Improved foreign key relationships
- Additional validation rules
- Performance index additions
- Data cleanup and normalization

### 6. `20250622014550_add_missing_tables`
**Date**: June 22, 2025  
**Purpose**: Add remaining tables for document management

**Tables Added:**
- `ingested_documents` - Track uploaded insurance documents
- `document_chunks` - Chunked content for vector processing
- `glossary_terms` - Dental terminology definitions
- `insurance_data` - General insurance policy data

**Features:**
- Document processing pipeline
- Chunk-based vector storage
- UUID-based document identification
- Processing status tracking

**SQL Preview:**
```sql
-- Document ingestion tracking
CREATE TABLE ingested_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename TEXT NOT NULL,
    insurer TEXT,
    procedure_code TEXT,
    document_type TEXT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    file_size BIGINT,
    file_path TEXT,
    processing_status TEXT DEFAULT 'pending',
    storage_path TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document chunks for vector processing
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES ingested_documents(id),
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding VECTOR(1536),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Schema Evolution

### Phase 1: Basic Insurance Data (March 2024)
- **Focus**: Core insurance carrier and procedure management
- **Technology**: Traditional relational model with JSONB for flexibility
- **Use Case**: Manual insurance data lookup and management

### Phase 2: Document Upload System (March 2024)
- **Focus**: Bulk data ingestion from carriers
- **Technology**: JSON upload and processing pipeline
- **Use Case**: Automated data updates from carrier sources

### Phase 3: RAG Integration (June 2025)
- **Focus**: AI-powered semantic search and narrative generation
- **Technology**: Vector embeddings with pgvector extension
- **Use Case**: Intelligent guideline search and compliance checking

### Phase 4: Document Processing Pipeline (June 2025)
- **Focus**: Full document lifecycle management
- **Technology**: UUID-based tracking with chunk processing
- **Use Case**: Large document ingestion with vector-based retrieval

## Migration Patterns

### Data Migration Strategy

**1. Additive Changes:**
- New tables added without affecting existing data
- Backward compatibility maintained
- Graceful degradation for missing features

**2. Schema Enhancements:**
- Foreign keys added for referential integrity
- Indexes created for performance optimization
- Constraints added for data validation

**3. Function Evolution:**
- Vector search functions added incrementally
- Performance optimizations applied
- Security improvements implemented

### Rollback Procedures

**Current Rollback Capabilities:**

```sql
-- Rollback to before vector tables (if needed)
DROP TABLE IF EXISTS document_chunks CASCADE;
DROP TABLE IF EXISTS ingested_documents CASCADE;
DROP TABLE IF EXISTS search_requests CASCADE;
DROP TABLE IF EXISTS query_logs CASCADE;
DROP TABLE IF EXISTS search_logs CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS embeddings CASCADE;
DROP FUNCTION IF EXISTS search_guidelines_vector;
DROP FUNCTION IF EXISTS search_guidelines_hybrid;
DROP EXTENSION IF EXISTS vector;
```

**Data Preservation:**
- Core insurance tables remain intact
- Carrier and procedure data preserved
- Only RAG-specific features removed

### Performance Impact

**Migration Performance Metrics:**

| Migration | Duration | Tables Affected | Downtime |
|-----------|----------|-----------------|----------|
| 20240325000000 | ~5 minutes | 12 new tables | None (new DB) |
| 20240325010000 | ~30 seconds | 1 new table | None |
| 20240325020000 | ~15 seconds | Indexes only | None |
| 20250622013224 | ~10 minutes | 5 new tables + functions | Minimal |
| 20250622014512 | ~20 minutes | All tables (data migration) | 5 minutes |
| 20250622014550 | ~5 minutes | 4 new tables | None |

**Index Creation Impact:**
- Vector indexes require significant time for large datasets
- Use `CONCURRENTLY` for production deployments
- Monitor index build progress with `pg_stat_progress_create_index`

## Current Schema State

### Table Summary

| Category | Tables | Purpose |
|----------|---------|---------|
| **Core Insurance** | insurance_carriers, procedures, guidelines | Primary business data |
| **Relationships** | carrier_aliases, carrier_procedure_requirements | Data relationships |
| **Networks** | insurance_networks, network_carrier_relationships | Network management |
| **Plans** | insurance_plans, medicare_advantage_plans | Plan information |
| **Operations** | appeal_procedures, documentation_requirements | Operational processes |
| **Vector/RAG** | embeddings, documents, document_chunks | AI/ML capabilities |
| **Analytics** | search_logs, query_logs, search_requests | Performance monitoring |
| **Utilities** | json_uploads, glossary_terms, insurance_data | Supporting features |

### Constraint Summary

**Primary Keys:** All tables have surrogate primary keys (BIGSERIAL or UUID)

**Foreign Keys:** 11 foreign key relationships ensuring referential integrity

**Indexes:** 
- Standard B-tree indexes on foreign keys and common queries
- HNSW vector indexes for similarity search
- GIN indexes for JSONB queries

**Check Constraints:**
- Data validation on enum-like fields
- Date range validations
- Required field constraints

## Future Migration Planning

### Planned Enhancements

**1. Row Level Security (RLS)**
```sql
-- Enable RLS on all public tables
ALTER TABLE insurance_carriers ENABLE ROW LEVEL SECURITY;
-- Add appropriate policies
```

**2. Audit Logging**
```sql
-- Add audit columns to existing tables
ALTER TABLE insurance_carriers ADD COLUMN audit_user TEXT;
ALTER TABLE insurance_carriers ADD COLUMN audit_action TEXT;
```

**3. Partitioning for Large Tables**
```sql
-- Partition embeddings by date for performance
ALTER TABLE embeddings PARTITION BY RANGE (created_at);
```

**4. Enhanced Indexes**
```sql
-- Optimize foreign key queries
CREATE INDEX CONCURRENTLY idx_guidelines_carrier_category 
ON guidelines(carrier_id, category) 
WHERE expiration_date IS NULL OR expiration_date > CURRENT_DATE;
```

### Migration Best Practices

**1. Pre-Migration Checklist:**
- [ ] Backup current database
- [ ] Test migration on staging environment
- [ ] Verify application compatibility
- [ ] Plan rollback procedure
- [ ] Schedule maintenance window if needed

**2. During Migration:**
- [ ] Monitor migration progress
- [ ] Check for blocking queries
- [ ] Verify data integrity
- [ ] Test critical functions

**3. Post-Migration:**
- [ ] Update table statistics with `ANALYZE`
- [ ] Verify all indexes are being used
- [ ] Test application functionality
- [ ] Monitor performance metrics

### Version Control

**Migration Naming Convention:**
`YYYYMMDD_HHMMSS_descriptive_name.sql`

**Git Integration:**
- All migrations tracked in version control
- Migration scripts include both up and down operations
- Automated testing for migration compatibility

**Environment Promotion:**
1. **Development**: Test new migrations
2. **Staging**: Validate with production-like data
3. **Production**: Execute with monitoring

## Troubleshooting

### Common Migration Issues

**1. Vector Extension Installation:**
```sql
-- If vector extension fails to install
CREATE EXTENSION IF NOT EXISTS vector;
-- Check extension availability
SELECT * FROM pg_available_extensions WHERE name = 'vector';
```

**2. Large Table Migrations:**
```sql
-- Monitor long-running migrations
SELECT query, state, query_start 
FROM pg_stat_activity 
WHERE query LIKE '%ALTER TABLE%';
```

**3. Index Build Monitoring:**
```sql
-- Check index build progress
SELECT 
    datname,
    pid,
    phase,
    blocks_total,
    blocks_done,
    tuples_total,
    tuples_done
FROM pg_stat_progress_create_index;
```

**4. Foreign Key Validation:**
```sql
-- Check for orphaned records before adding FK
SELECT DISTINCT carrier_id 
FROM guidelines g
WHERE NOT EXISTS (
    SELECT 1 FROM insurance_carriers ic 
    WHERE ic.id = g.carrier_id
);
```

### Recovery Procedures

**Complete Database Restore:**
1. Stop application connections
2. Restore from backup: `pg_restore backup_file.dump`
3. Verify data integrity
4. Resume application connections

**Selective Table Restore:**
1. Export current table: `pg_dump --table=table_name`
2. Drop problematic table
3. Restore from backup
4. Re-apply recent changes if needed

**Migration Rollback:**
1. Execute rollback script
2. Verify data consistency
3. Update application configuration
4. Test critical functionality