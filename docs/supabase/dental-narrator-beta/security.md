# Security Documentation
**Project**: dental-narrator-beta  
**Generated**: 2025-06-26

## Security Status Overview

⚠️ **CRITICAL SECURITY ISSUES DETECTED**

The database has multiple security vulnerabilities that need immediate attention:
- **24 tables** without Row Level Security (RLS) enabled
- **2 functions** with mutable search paths
- **1 extension** installed in public schema

## Row Level Security (RLS) Issues

### ❌ Tables Missing RLS (24 Critical Issues)

All public tables are missing Row Level Security policies, exposing data to unauthorized access:

1. `appeal_procedures` - Appeal process data
2. `carrier_aliases` - Carrier name variations  
3. `carrier_procedure_requirements` - Procedure requirements
4. `credentialing_requirements` - Provider credentials
5. `document_chunks` - Document content chunks
6. `documentation_requirements` - Required documentation
7. `documents` - Document storage
8. `embeddings` - Vector embeddings
9. `glossary_terms` - Terminology definitions
10. `guidelines` - Insurance guidelines
11. `ingested_documents` - Uploaded documents
12. `insurance_carriers` - Carrier information
13. `insurance_data` - Policy information
14. `insurance_networks` - Network details
15. `insurance_plans` - Plan information
16. `medicare_advantage_plans` - Medicare plans
17. `network_carrier_relationships` - Network relationships
18. `procedures` - CDT procedure codes
19. `processing_caveats` - Processing notes
20. `query_logs` - Query tracking
21. `search_logs` - Search analytics
22. `search_requests` - Search requests

### 🔧 **Immediate Action Required**

**For each table, implement RLS:**

```sql
-- Enable RLS on each table
ALTER TABLE public.table_name ENABLE ROW LEVEL SECURITY;

-- Create appropriate policies based on access patterns
-- Example for read-only data (carriers, procedures, guidelines):
CREATE POLICY "Allow read access to authenticated users" ON public.insurance_carriers
    FOR SELECT TO authenticated USING (true);

-- Example for user-specific data (logs, requests):
CREATE POLICY "Users can only see their own data" ON public.search_logs
    FOR ALL TO authenticated USING (auth.uid() = user_id);
```

**Recommended RLS Policies by Table Type:**

**Public Reference Data** (carriers, procedures, guidelines):
- Allow SELECT for authenticated users
- Restrict INSERT/UPDATE/DELETE to admin roles

**User-Generated Content** (logs, requests, uploads):
- Users can only access their own records
- Use `auth.uid()` for user-based filtering

**Administrative Data** (requirements, caveats):
- Restrict to admin/service roles only
- No public access

## Function Security Issues

### ⚠️ Functions with Mutable Search Paths (2 Warnings)

These functions don't have fixed search paths, creating potential security risks:

1. **`search_guidelines_vector`**
   - **Risk**: Function behavior could be altered by changing search_path
   - **Fix**: Set search_path in function definition

2. **`search_guidelines_hybrid`**
   - **Risk**: Function behavior could be altered by changing search_path  
   - **Fix**: Set search_path in function definition

### 🔧 **Remediation for Functions**

```sql
-- Fix search_guidelines_vector function
CREATE OR REPLACE FUNCTION search_guidelines_vector(...)
RETURNS TABLE(...)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
-- function body
$$;

-- Fix search_guidelines_hybrid function  
CREATE OR REPLACE FUNCTION search_guidelines_hybrid(...)
RETURNS TABLE(...)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
-- function body
$$;
```

## Extension Security Issues

### ⚠️ Extension in Public Schema (1 Warning)

**`vector` extension** is installed in the public schema:
- **Risk**: Extensions in public schema can be accessed by all users
- **Recommendation**: Move to dedicated schema

### 🔧 **Extension Remediation**

```sql
-- Create dedicated schema for extensions
CREATE SCHEMA IF NOT EXISTS extensions;

-- Move vector extension (requires recreation)
DROP EXTENSION vector;
CREATE EXTENSION vector SCHEMA extensions;

-- Update any references to use extensions.vector types
```

## Security Best Practices

### Authentication & Authorization

**Current State**: No authentication policies in place
**Recommendation**: Implement comprehensive auth strategy

```sql
-- Example: Create admin role
CREATE ROLE dental_admin;
GRANT ALL ON ALL TABLES IN SCHEMA public TO dental_admin;

-- Example: Create read-only role for applications
CREATE ROLE dental_app;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO dental_app;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO dental_app;
```

### Data Access Patterns

**Recommended Access Control:**

1. **Application Level**: Service account with specific permissions
2. **User Level**: RLS policies based on user context
3. **Admin Level**: Full access with audit logging

### Sensitive Data Handling

**Tables containing sensitive information:**
- `insurance_data` - Policy and coverage information
- `ingested_documents` - Uploaded insurance documents
- `document_chunks` - Document content
- `search_logs` - User search behavior

**Recommendations:**
- Encrypt sensitive fields at application level
- Implement data retention policies
- Add audit logging for access to sensitive tables
- Consider pseudonymization for analytics

### API Security

**For Supabase API access:**

```sql
-- Example RLS policy for API access
CREATE POLICY "API users can read guidelines" ON guidelines
    FOR SELECT TO anon USING (
        -- Only allow access to non-sensitive guidelines
        category NOT IN ('internal', 'confidential')
    );

-- Service role policy for full access
CREATE POLICY "Service role full access" ON guidelines
    FOR ALL TO service_role USING (true);
```

## Monitoring & Auditing

### Security Event Logging

**Implement audit logging:**

```sql
-- Create audit log table
CREATE TABLE security_audit_log (
    id BIGSERIAL PRIMARY KEY,
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL,
    user_id UUID,
    old_values JSONB,
    new_values JSONB,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on audit log
ALTER TABLE security_audit_log ENABLE ROW LEVEL SECURITY;

-- Audit log policies
CREATE POLICY "Users can only see their own audit logs" ON security_audit_log
    FOR SELECT TO authenticated USING (auth.uid() = user_id);
```

### Security Monitoring Queries

```sql
-- Monitor failed authentication attempts
SELECT COUNT(*) as failed_attempts, date_trunc('hour', timestamp) as hour
FROM auth.audit_log_entries 
WHERE event_type = 'AUTHENTICATION_FAILED'
GROUP BY hour
ORDER BY hour DESC;

-- Monitor suspicious access patterns
SELECT user_id, COUNT(*) as query_count, 
       array_agg(DISTINCT table_name) as tables_accessed
FROM security_audit_log
WHERE timestamp > NOW() - INTERVAL '1 hour'
GROUP BY user_id
HAVING COUNT(*) > 100;  -- Adjust threshold as needed
```

## Compliance Considerations

### HIPAA Compliance (if applicable)
- Enable audit logging for all PHI access
- Implement data encryption at rest and in transit
- Regular access reviews and user provisioning audits

### Data Privacy
- Implement data retention policies
- Add user consent tracking
- Enable data export/deletion capabilities

## Remediation Priority

### 🔴 **Critical (Immediate)**
1. Enable RLS on all public tables
2. Implement basic authentication policies
3. Secure function search paths

### 🟡 **High (This Week)**
1. Move vector extension to dedicated schema
2. Implement audit logging
3. Create service accounts with minimal permissions

### 🟢 **Medium (Next Sprint)**
1. Implement data retention policies
2. Add encryption for sensitive fields
3. Set up security monitoring queries

## Additional Resources

- [Supabase RLS Documentation](https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public)
- [PostgreSQL Security Best Practices](https://www.postgresql.org/docs/current/security.html)
- [Function Security Guidelines](https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable)