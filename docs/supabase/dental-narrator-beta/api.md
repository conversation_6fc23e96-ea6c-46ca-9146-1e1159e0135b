# API Documentation
**Project**: dental-narrator-beta  
**Base URL**: https://ymivwfdmeymosgvgoibb.supabase.co  
**Generated**: 2025-06-26

## Overview

The Dental Narrator API provides REST and GraphQL endpoints for accessing insurance carrier data, generating narratives, and performing vector searches. The API is built on Supabase's auto-generated REST API with custom functions for specialized operations.

## Authentication

### API Keys

**Anonymous Key (Public):**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InltaXZ3ZmRtZXltb3NndmdvaWJiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI1MDYwMDYsImV4cCI6MjA1ODA4MjAwNn0.kBLt7Yv-dHekFbyq_w9qTuxN6QEGiXt20jOy8M7xsKE
```

**Service Key (Private - Server-side only):**
Contact administrator for service role key.

### Usage

Include API key in request headers:
```http
Authorization: Bearer YOUR_API_KEY
apikey: YOUR_API_KEY
Content-Type: application/json
```

## Base URLs

### REST API
```
https://ymivwfdmeymosgvgoibb.supabase.co/rest/v1/
```

### GraphQL API  
```
https://ymivwfdmeymosgvgoibb.supabase.co/graphql/v1
```

### Realtime API
```
wss://ymivwfdmeymosgvgoibb.supabase.co/realtime/v1/websocket
```

## REST API Endpoints

### Insurance Carriers

#### Get All Carriers
```http
GET /insurance_carriers
```

**Query Parameters:**
- `select`: Columns to return (default: all)
- `limit`: Number of records (default: 1000)
- `offset`: Pagination offset
- `order`: Sort order (e.g., `carrier_name.asc`)

**Example:**
```bash
curl -X GET \
  'https://ymivwfdmeymosgvgoibb.supabase.co/rest/v1/insurance_carriers?select=*&order=carrier_name.asc' \
  -H 'apikey: YOUR_API_KEY' \
  -H 'Authorization: Bearer YOUR_API_KEY'
```

**Response:**
```json
[
  {
    "id": 1,
    "carrier_name": "Delta Dental",
    "carrier_type": "PPO",
    "payer_id": "DDPA",
    "claims_address": "P.O. Box 1234, Dallas, TX 75201",
    "phone_number": "**************",
    "website": "https://www.deltadental.com",
    "contact_info": {
      "customer_service": "**************",
      "provider_relations": "**************"
    },
    "created_at": "2024-03-25T00:00:00Z",
    "updated_at": "2024-03-25T00:00:00Z"
  }
]
```

#### Get Carrier by ID
```http
GET /insurance_carriers?id=eq.{id}
```

#### Search Carriers by Name
```http
GET /insurance_carriers?carrier_name=ilike.*{name}*
```

### Procedures

#### Get All Procedures
```http
GET /procedures
```

**Example:**
```bash
curl -X GET \
  'https://ymivwfdmeymosgvgoibb.supabase.co/rest/v1/procedures?select=*&limit=100' \
  -H 'apikey: YOUR_API_KEY'
```

#### Search Procedures by Code
```http
GET /procedures?procedure_code=eq.{code}
```

#### Search Procedures by Category
```http
GET /procedures?category=eq.{category}
```

### Guidelines

#### Get Guidelines for Carrier
```http
GET /guidelines?carrier_id=eq.{carrier_id}
```

#### Get Active Guidelines
```http
GET /guidelines?and=(effective_date.lte.today,or(expiration_date.is.null,expiration_date.gt.today))
```

### Vector Search Functions

#### Search Guidelines (Vector)
```http
POST /rpc/search_guidelines_vector
```

**Request Body:**
```json
{
  "query_embedding": "[0.1, 0.2, 0.3, ...]",
  "content_type_filter": "guideline",
  "carrier_id_filter": 1,
  "category_filter": "preventive",
  "similarity_threshold": 0.8,
  "max_results": 10
}
```

**Example:**
```bash
curl -X POST \
  'https://ymivwfdmeymosgvgoibb.supabase.co/rest/v1/rpc/search_guidelines_vector' \
  -H 'apikey: YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "query_embedding": "[0.1, 0.2, ...]",
    "carrier_id_filter": 1,
    "similarity_threshold": 0.8,
    "max_results": 5
  }'
```

**Response:**
```json
[
  {
    "id": 123,
    "content_id": 456,
    "similarity_score": 0.89,
    "metadata": {
      "carrier_id": 1,
      "category": "preventive",
      "procedure_codes": ["D1110", "D1120"]
    },
    "created_at": "2024-03-25T10:30:00Z"
  }
]
```

#### Search Guidelines (Hybrid)
```http
POST /rpc/search_guidelines_hybrid
```

**Request Body:**
```json
{
  "query_embedding": "[0.1, 0.2, 0.3, ...]",
  "carrier_id_filter": 1,
  "category_filter": "restorative",
  "similarity_threshold": 0.7,
  "max_results": 10
}
```

### Document Management

#### Get Ingested Documents
```http
GET /ingested_documents
```

#### Upload Document Metadata
```http
POST /ingested_documents
```

**Request Body:**
```json
{
  "filename": "delta_dental_guidelines.pdf",
  "insurer": "Delta Dental",
  "document_type": "guidelines",
  "file_size": 1024000,
  "processing_status": "pending"
}
```

### Logging and Analytics

#### Search Logs
```http
GET /search_logs
```

#### Query Performance Logs
```http
GET /query_logs
```

## TypeScript Types

### Core Types

```typescript
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      insurance_carriers: {
        Row: {
          id: number
          carrier_name: string
          carrier_type: string | null
          payer_id: string | null
          claims_address: string | null
          phone_number: string | null
          website: string | null
          contact_info: Json | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          carrier_name: string
          carrier_type?: string | null
          payer_id?: string | null
          claims_address?: string | null
          phone_number?: string | null
          website?: string | null
          contact_info?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          carrier_name?: string
          carrier_type?: string | null
          payer_id?: string | null
          claims_address?: string | null
          phone_number?: string | null
          website?: string | null
          contact_info?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      // ... other tables
    }
    Functions: {
      search_guidelines_vector: {
        Args: {
          query_embedding: string
          content_type_filter?: string
          carrier_id_filter?: number
          category_filter?: string
          similarity_threshold?: number
          max_results?: number
        }
        Returns: {
          id: number
          content_id: number
          similarity_score: number
          metadata: Json
          created_at: string
        }[]
      }
      search_guidelines_hybrid: {
        Args: {
          query_embedding: string
          carrier_id_filter?: number
          category_filter?: string
          similarity_threshold?: number
          max_results?: number
        }
        Returns: {
          id: number
          content_id: number
          similarity_score: number
          recency_score: number
          hybrid_score: number
          metadata: Json
        }[]
      }
    }
  }
}

// Utility types
export type Tables<T extends keyof Database['public']['Tables']> = 
  Database['public']['Tables'][T]['Row']

export type Inserts<T extends keyof Database['public']['Tables']> = 
  Database['public']['Tables'][T]['Insert']

export type Updates<T extends keyof Database['public']['Tables']> = 
  Database['public']['Tables'][T]['Update']
```

### Function Types

```typescript
// Vector search result
export interface VectorSearchResult {
  id: number
  content_id: number
  similarity_score: number
  metadata: Record<string, any>
  created_at: string
}

// Hybrid search result
export interface HybridSearchResult extends VectorSearchResult {
  recency_score: number
  hybrid_score: number
}

// Search parameters
export interface VectorSearchParams {
  query_embedding: number[]
  content_type_filter?: string
  carrier_id_filter?: number
  category_filter?: string
  similarity_threshold?: number
  max_results?: number
}
```

## JavaScript/TypeScript Client Usage

### Installation

```bash
npm install @supabase/supabase-js
```

### Client Setup

```typescript
import { createClient } from '@supabase/supabase-js'
import type { Database } from './types/database'

const supabaseUrl = 'https://ymivwfdmeymosgvgoibb.supabase.co'
const supabaseKey = 'YOUR_API_KEY'

export const supabase = createClient<Database>(supabaseUrl, supabaseKey)
```

### Example Usage

```typescript
// Get all carriers
const { data: carriers, error } = await supabase
  .from('insurance_carriers')
  .select('*')
  .order('carrier_name')

// Search procedures by code
const { data: procedure } = await supabase
  .from('procedures')
  .select('*')
  .eq('procedure_code', 'D1110')
  .single()

// Vector search
const { data: searchResults } = await supabase
  .rpc('search_guidelines_vector', {
    query_embedding: embeddingVector,
    carrier_id_filter: 1,
    similarity_threshold: 0.8,
    max_results: 10
  })

// Get guidelines with carrier info
const { data: guidelines } = await supabase
  .from('guidelines')
  .select(`
    *,
    insurance_carriers (
      carrier_name,
      carrier_type
    )
  `)
  .eq('carrier_id', carrierId)
```

## GraphQL API

### Schema Introspection

```graphql
query IntrospectionQuery {
  __schema {
    types {
      name
      kind
    }
  }
}
```

### Example Queries

#### Get Carriers with Procedures
```graphql
query GetCarriersWithProcedures {
  insurance_carriersCollection {
    edges {
      node {
        id
        carrier_name
        carrier_type
        carrier_procedure_requirementsCollection {
          edges {
            node {
              documentation_required
              frequency_limitation
              procedures {
                procedure_code
                description
              }
            }
          }
        }
      }
    }
  }
}
```

#### Get Guidelines by Category
```graphql
query GetGuidelinesByCategory($category: String!) {
  guidelinesCollection(filter: { category: { eq: $category } }) {
    edges {
      node {
        id
        title
        content
        effective_date
        expiration_date
        insurance_carriers {
          carrier_name
        }
      }
    }
  }
}
```

## Error Handling

### Common Error Responses

#### Authentication Error (401)
```json
{
  "message": "Invalid API key"
}
```

#### Not Found (404)
```json
{
  "message": "The result contains 0 rows"
}
```

#### Validation Error (400)
```json
{
  "message": "invalid input syntax for type bigint",
  "details": "...",
  "hint": "...",
  "code": "22P02"
}
```

### Error Handling in Code

```typescript
try {
  const { data, error } = await supabase
    .from('insurance_carriers')
    .select('*')
    .eq('id', carrierId)
    .single()

  if (error) {
    console.error('Database error:', error.message)
    return
  }

  // Use data
  console.log(data)
} catch (err) {
  console.error('Network error:', err)
}
```

## Rate Limiting

### Default Limits
- **Anonymous**: 100 requests per minute
- **Authenticated**: 1000 requests per minute  
- **Service Role**: 10000 requests per minute

### Best Practices
1. Implement client-side caching for static data
2. Use pagination for large datasets
3. Batch related queries when possible
4. Consider using Supabase's built-in caching

## Real-time Features

### Listening to Changes

```typescript
// Listen to carrier changes
const carrierSubscription = supabase
  .channel('carriers')
  .on('postgres_changes', 
    { event: '*', schema: 'public', table: 'insurance_carriers' },
    (payload) => {
      console.log('Carrier changed:', payload)
    }
  )
  .subscribe()

// Listen to new guidelines
const guidelineSubscription = supabase
  .channel('guidelines')
  .on('postgres_changes',
    { event: 'INSERT', schema: 'public', table: 'guidelines' },
    (payload) => {
      console.log('New guideline:', payload.new)
    }
  )
  .subscribe()
```

## Performance Optimization

### Query Optimization

#### Use Select to Limit Columns
```typescript
// Good: Only get needed columns
const { data } = await supabase
  .from('insurance_carriers')
  .select('id, carrier_name, phone_number')

// Avoid: Getting all columns when not needed
const { data } = await supabase
  .from('insurance_carriers')
  .select('*')
```

#### Use Pagination
```typescript
const { data } = await supabase
  .from('guidelines')
  .select('*')
  .range(0, 49)  // Get first 50 records
```

#### Optimize Vector Searches
```typescript
// Use appropriate similarity threshold
const { data } = await supabase
  .rpc('search_guidelines_vector', {
    query_embedding: embedding,
    similarity_threshold: 0.8,  // Higher threshold = fewer, more relevant results
    max_results: 10            // Limit results for performance
  })
```

### Caching Strategies

```typescript
// Cache static reference data
const carrierCache = new Map()

async function getCarrier(id: number) {
  if (carrierCache.has(id)) {
    return carrierCache.get(id)
  }
  
  const { data } = await supabase
    .from('insurance_carriers')
    .select('*')
    .eq('id', id)
    .single()
  
  if (data) {
    carrierCache.set(id, data)
  }
  
  return data
}
```

## Security Best Practices

### Row Level Security

When RLS is enabled (recommended), queries automatically filter based on user context:

```typescript
// This will only return data the authenticated user can access
const { data } = await supabase
  .from('search_logs')
  .select('*')
```

### Input Validation

```typescript
function validateCarrierId(id: any): number {
  const carrierId = parseInt(id)
  if (isNaN(carrierId) || carrierId <= 0) {
    throw new Error('Invalid carrier ID')
  }
  return carrierId
}

// Use in queries
const carrierId = validateCarrierId(req.params.id)
const { data } = await supabase
  .from('insurance_carriers')
  .select('*')
  .eq('id', carrierId)
```

### Environment Variables

```typescript
// Use environment variables for sensitive data
const supabaseUrl = process.env.SUPABASE_URL
const supabaseKey = process.env.SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase configuration')
}
```