# Functions Documentation
**Project**: dental-narrator-beta  
**Generated**: 2025-06-26

## Overview

The dental-narrator-beta database includes vector search functions, database extensions, and utility functions to support the RAG-based narrative generation system.

## Database Functions

### Vector Search Functions

#### `search_guidelines_vector`
Performs vector similarity search on insurance guidelines using cosine similarity.

**Signature:**
```sql
search_guidelines_vector(
    query_embedding vector(1536),
    content_type_filter text DEFAULT NULL,
    carrier_id_filter bigint DEFAULT NULL,
    category_filter text DEFAULT NULL,
    similarity_threshold float DEFAULT 0.7,
    max_results integer DEFAULT 10
)
```

**Parameters:**
- `query_embedding`: 1536-dimensional vector representing the search query
- `content_type_filter`: Optional filter by content type (e.g., 'guideline', 'procedure')
- `carrier_id_filter`: Optional filter by insurance carrier ID
- `category_filter`: Optional filter by guideline category
- `similarity_threshold`: Minimum similarity score (0.0 to 1.0, default 0.7)
- `max_results`: Maximum number of results to return (default 10)

**Returns:**
```sql
TABLE(
    id bigint,
    content_id bigint,
    similarity_score float,
    metadata jsonb,
    created_at timestamptz
)
```

**Usage Example:**
```sql
-- Search for preventive care guidelines from Delta Dental
SELECT * FROM search_guidelines_vector(
    '[1536-dimensional embedding vector]',
    'guideline',
    1,  -- Delta Dental carrier ID
    'preventive',
    0.8,
    5
);
```

**Performance Notes:**
- Uses HNSW index for fast vector similarity search
- Cosine similarity: `1 - (embedding <=> query_embedding)`
- Results ordered by similarity score (highest first)

#### `search_guidelines_hybrid`
Performs hybrid search combining vector similarity with recency scoring.

**Signature:**
```sql
search_guidelines_hybrid(
    query_embedding vector(1536),
    carrier_id_filter bigint DEFAULT NULL,
    category_filter text DEFAULT NULL,
    similarity_threshold float DEFAULT 0.7,
    max_results integer DEFAULT 10
)
```

**Parameters:**
- `query_embedding`: 1536-dimensional vector representing the search query
- `carrier_id_filter`: Optional filter by insurance carrier ID
- `category_filter`: Optional filter by guideline category
- `similarity_threshold`: Minimum similarity score (0.0 to 1.0, default 0.7)
- `max_results`: Maximum number of results to return (default 10)

**Returns:**
```sql
TABLE(
    id bigint,
    content_id bigint,
    similarity_score float,
    recency_score float,
    hybrid_score float,
    metadata jsonb
)
```

**Scoring Algorithm:**
- `similarity_score`: Vector cosine similarity (0.0 to 1.0)
- `recency_score`: Time-based score favoring newer content
- `hybrid_score`: Weighted combination of similarity and recency

**Usage Example:**
```sql
-- Hybrid search balancing relevance and recency
SELECT * FROM search_guidelines_hybrid(
    '[1536-dimensional embedding vector]',
    2,  -- Cigna carrier ID
    'restorative',
    0.7,
    10
);
```

### Vector Utility Functions

#### `l2_norm`
Calculates the L2 norm (Euclidean norm) of a vector.

**Signatures:**
```sql
l2_norm(vector) → float
l2_norm(halfvec) → float
```

**Usage:**
```sql
SELECT l2_norm(embedding) FROM embeddings LIMIT 1;
```

#### `l2_normalize`
Normalizes a vector to unit length using L2 normalization.

**Signatures:**
```sql
l2_normalize(vector) → vector
l2_normalize(halfvec) → halfvec
l2_normalize(sparsevec) → sparsevec
```

**Usage:**
```sql
-- Normalize embedding before storage
INSERT INTO embeddings (content_id, content_type, embedding)
VALUES (1, 'guideline', l2_normalize('[1,2,3,...]'));
```

#### `vector_dims`
Returns the dimensionality of a vector.

**Signatures:**
```sql
vector_dims(vector) → integer
vector_dims(halfvec) → integer
```

**Usage:**
```sql
SELECT vector_dims(embedding) FROM embeddings LIMIT 1;
-- Returns: 1536
```

#### `vector_norm`
Calculates the norm of a vector.

**Signature:**
```sql
vector_norm(vector) → float
```

### Vector Aggregation Functions

#### `vector_avg`
Calculates the average of multiple vectors.

**Signature:**
```sql
vector_avg(vector[]) → vector
```

**Usage:**
```sql
-- Calculate average embedding for a carrier's guidelines
SELECT vector_avg(ARRAY_AGG(e.embedding)) as avg_embedding
FROM embeddings e
JOIN guidelines g ON e.content_id = g.id
WHERE g.carrier_id = 1;
```

#### `halfvec_avg`
Calculates the average of multiple half-precision vectors.

**Signature:**
```sql
halfvec_avg(halfvec[]) → halfvec
```

### Binary Quantization Functions

#### `binary_quantize`
Converts vectors to binary representation for storage optimization.

**Signatures:**
```sql
binary_quantize(vector) → bit
binary_quantize(halfvec) → bit
```

**Usage:**
```sql
-- Store binary quantized version for faster approximate search
UPDATE embeddings 
SET binary_embedding = binary_quantize(embedding)
WHERE binary_embedding IS NULL;
```

## Vector Index Functions

### HNSW Index Support

#### `hnswhandler`
Internal handler for HNSW (Hierarchical Navigable Small World) indexes.

**Purpose:**
- Manages HNSW index creation and maintenance
- Optimizes approximate nearest neighbor search
- Provides sub-linear search time complexity

#### `hnsw_*_support` Functions
Support functions for different vector types in HNSW indexes:
- `hnsw_bit_support`: Binary vector support
- `hnsw_halfvec_support`: Half-precision vector support  
- `hnsw_sparsevec_support`: Sparse vector support

### IVF Index Support

#### `ivfflathandler`
Internal handler for IVF (Inverted File) flat indexes.

**Purpose:**
- Manages IVF index creation for approximate search
- Partitions vector space for efficient querying
- Balances search accuracy and performance

#### `ivfflat_*_support` Functions
Support functions for different vector types in IVF indexes:
- `ivfflat_bit_support`: Binary vector support
- `ivfflat_halfvec_support`: Half-precision vector support

## Vector Type Functions

### Vector Input/Output

#### `vector_out`
Converts internal vector representation to text format.

**Signature:**
```sql
vector_out(vector) → cstring
```

#### `vector_send`
Converts vector to binary format for network transmission.

**Signature:**
```sql
vector_send(vector) → bytea
```

#### `vector_typmod_in`
Handles vector type modifiers (dimensionality constraints).

**Signature:**
```sql
vector_typmod_in(cstring[]) → integer
```

### Half-Precision Vector Functions

#### `halfvec_out`
Output function for half-precision vectors.

#### `halfvec_send`
Binary output function for half-precision vectors.

#### `halfvec_typmod_in`
Type modifier function for half-precision vectors.

### Sparse Vector Functions

#### `sparsevec_out`
Output function for sparse vectors.

#### `sparsevec_send`
Binary output function for sparse vectors.

#### `sparsevec_typmod_in`
Type modifier function for sparse vectors.

## Extensions

### Vector Extension
**Name**: `vector`  
**Version**: 0.8.0  
**Schema**: public  
**Status**: ✅ Installed

**Features:**
- Vector data types (vector, halfvec, sparsevec)
- HNSW and IVF index methods
- Distance operations (L2, cosine, inner product)
- Vector aggregation and utility functions

**Key Operators:**
- `<->`: L2 distance
- `<#>`: Negative inner product
- `<=>`: Cosine distance
- `<+>`: L1 distance

### pgcrypto Extension
**Name**: `pgcrypto`  
**Version**: 1.3  
**Schema**: extensions  
**Status**: ✅ Installed

**Features:**
- Cryptographic functions for data security
- Hash functions (MD5, SHA1, SHA224, SHA256, SHA384, SHA512)
- Encryption/decryption functions
- Random data generation

### uuid-ossp Extension
**Name**: `uuid-ossp`  
**Version**: 1.1  
**Schema**: extensions  
**Status**: ✅ Installed

**Features:**
- UUID generation functions
- Various UUID algorithms
- Used for document and chunk IDs

### pgjwt Extension
**Name**: `pgjwt`  
**Version**: 0.2.0  
**Schema**: extensions  
**Status**: ✅ Installed

**Features:**
- JSON Web Token (JWT) support
- Token generation and verification
- Authentication helper functions

### pg_stat_statements Extension
**Name**: `pg_stat_statements`  
**Version**: 1.10  
**Schema**: extensions  
**Status**: ✅ Installed

**Features:**
- Query performance statistics
- Execution time tracking
- Query optimization insights

### pg_graphql Extension
**Name**: `pg_graphql`  
**Version**: 1.5.11  
**Schema**: graphql  
**Status**: ✅ Installed

**Features:**
- GraphQL API generation from database schema
- Automatic query optimization
- Real-time subscriptions

### pgsodium Extension
**Name**: `pgsodium`  
**Version**: 3.1.8  
**Schema**: pgsodium  
**Status**: ✅ Installed

**Features:**
- Modern cryptography functions
- Encryption at rest
- Key management

### supabase_vault Extension
**Name**: `supabase_vault`  
**Version**: 0.3.1  
**Schema**: vault  
**Status**: ✅ Installed

**Features:**
- Secure secret storage
- Encryption key management
- Access control for sensitive data

## Usage Patterns

### Vector Search Pipeline

**1. Embedding Generation:**
```sql
-- Store new guideline with embedding
INSERT INTO guidelines (carrier_id, category, title, content)
VALUES (1, 'preventive', 'Cleaning Guidelines', '{"text": "..."}');

-- Generate and store embedding
INSERT INTO embeddings (content_type, content_id, embedding, metadata)
VALUES (
    'guideline',
    LASTVAL(),
    '[0.1, 0.2, ...]',  -- Generated by OpenAI API
    '{"carrier_id": 1, "category": "preventive"}'
);
```

**2. Similarity Search:**
```sql
-- Find similar guidelines
WITH query_vector AS (
    SELECT '[0.15, 0.25, ...]'::vector(1536) as embedding
)
SELECT 
    g.title,
    g.content,
    s.similarity_score
FROM query_vector q
CROSS JOIN search_guidelines_vector(
    q.embedding,
    'guideline',
    NULL,  -- Any carrier
    'preventive',
    0.8,
    5
) s
JOIN guidelines g ON s.content_id = g.id;
```

**3. Hybrid Search with Recency:**
```sql
-- Find relevant and recent guidelines
SELECT 
    g.title,
    g.effective_date,
    s.similarity_score,
    s.recency_score,
    s.hybrid_score
FROM search_guidelines_hybrid(
    '[0.15, 0.25, ...]'::vector(1536),
    1,  -- Delta Dental
    'restorative',
    0.7,
    10
) s
JOIN guidelines g ON s.content_id = g.id
ORDER BY s.hybrid_score DESC;
```

### Performance Optimization

**Index Creation:**
```sql
-- Create optimized HNSW index
CREATE INDEX CONCURRENTLY idx_embeddings_hnsw 
ON embeddings USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- Create metadata index for filtering
CREATE INDEX CONCURRENTLY idx_embeddings_metadata_carrier 
ON embeddings USING gin ((metadata->>'carrier_id'));
```

**Query Optimization:**
```sql
-- Use EXPLAIN ANALYZE to optimize search functions
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM search_guidelines_vector(
    '[...]'::vector(1536),
    'guideline',
    1,
    'preventive',
    0.8,
    10
);
```

## Security Considerations

### Function Security

**Current Issues:**
- Functions have mutable search_path (security warning)
- Need to set explicit search_path for security

**Recommended Fixes:**
```sql
-- Secure function definition
CREATE OR REPLACE FUNCTION search_guidelines_vector(...)
RETURNS TABLE(...)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
-- function body
$$;
```

### Access Control

**Function Permissions:**
```sql
-- Grant execute permission to specific roles
GRANT EXECUTE ON FUNCTION search_guidelines_vector TO dental_app;
GRANT EXECUTE ON FUNCTION search_guidelines_hybrid TO dental_app;

-- Revoke from public
REVOKE EXECUTE ON FUNCTION search_guidelines_vector FROM public;
```

## Troubleshooting

### Common Issues

**1. Vector Dimension Mismatch:**
```sql
-- Check vector dimensions
SELECT content_id, vector_dims(embedding) 
FROM embeddings 
WHERE vector_dims(embedding) != 1536;
```

**2. Index Not Being Used:**
```sql
-- Force index usage
SET enable_seqscan = off;
EXPLAIN SELECT * FROM search_guidelines_vector(...);
```

**3. Performance Issues:**
```sql
-- Check index bloat
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_total_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(indexrelid) DESC;
```

## Best Practices

### Vector Operations
1. **Normalize vectors** before storage for consistent similarity scores
2. **Use appropriate index parameters** for your data size and query patterns
3. **Monitor index usage** and adjust parameters as needed
4. **Batch vector operations** for better performance

### Function Usage
1. **Set explicit search_path** in all functions for security
2. **Use SECURITY DEFINER** carefully and only when necessary
3. **Add parameter validation** in complex functions
4. **Monitor function performance** with pg_stat_statements