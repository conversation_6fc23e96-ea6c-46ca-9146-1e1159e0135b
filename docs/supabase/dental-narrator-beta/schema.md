# Database Schema Documentation
**Project**: dental-narrator-beta  
**Environment**: us-west-1  
**Database Version**: PostgreSQL 15.8.1.100  
**Generated**: 2025-06-26

## Overview

The Dental Narrator database is designed to support a comprehensive RAG-based system for generating insurance-compliant narratives for dental procedures. The schema includes tables for insurance carriers, procedures, guidelines, and vector embeddings for semantic search capabilities.

## Tables Summary

| Table | Records | Purpose |
|-------|---------|---------|
| `insurance_carriers` | Insurance companies and contact information |
| `carrier_aliases` | Alternative names for carriers |  
| `procedures` | CDT codes and procedure descriptions |
| `guidelines` | Insurance carrier guidelines and policies |
| `embeddings` | Vector embeddings for semantic search |
| `appeal_procedures` | Insurance appeal processes |
| `documentation_requirements` | Required documentation by carrier |
| `credentialing_requirements` | Provider credentialing requirements |
| `processing_caveats` | Known processing issues and workarounds |

## Core Tables

### insurance_carriers
Primary table for insurance carrier information.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `carrier_name` | text | NO | | Insurance carrier name |
| `carrier_type` | text | YES | | Type of carrier (PPO, HMO, etc.) |
| `payer_id` | text | YES | | Electronic payer ID |
| `claims_address` | text | YES | | Claims submission address |
| `phone_number` | text | YES | | Contact phone number |
| `website` | text | YES | | Carrier website URL |
| `contact_info` | jsonb | YES | | Additional contact information |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

### procedures
CDT procedure codes and descriptions.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `procedure_code` | text | NO | | CDT procedure code |
| `description` | text | NO | | Procedure description |
| `category` | text | YES | | Procedure category |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

### guidelines
Insurance carrier guidelines and policies.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `carrier_id` | bigint | YES | | Foreign key to insurance_carriers |
| `category` | varchar | NO | | Guideline category |
| `title` | varchar | NO | | Guideline title |
| `content` | jsonb | NO | | Guideline content |
| `effective_date` | date | YES | | When guideline becomes effective |
| `expiration_date` | date | YES | | When guideline expires |
| `metadata` | jsonb | YES | | Additional metadata |
| `created_at` | timestamptz | YES | CURRENT_TIMESTAMP | Creation timestamp |
| `updated_at` | timestamptz | YES | CURRENT_TIMESTAMP | Last update timestamp |

**Relationships:**
- `carrier_id` → `insurance_carriers.id`

### embeddings
Vector embeddings for semantic search.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `content_type` | varchar | NO | | Type of content (guideline, procedure, etc.) |
| `content_id` | bigint | NO | | ID of the referenced content |
| `embedding` | vector | YES | | Vector embedding (1536 dimensions) |
| `metadata` | jsonb | YES | | Additional metadata |
| `created_at` | timestamptz | YES | CURRENT_TIMESTAMP | Creation timestamp |
| `updated_at` | timestamptz | YES | CURRENT_TIMESTAMP | Last update timestamp |

## Supporting Tables

### carrier_aliases
Alternative names for insurance carriers.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `carrier_id` | bigint | YES | | Foreign key to insurance_carriers |
| `alias_name` | text | NO | | Alternative carrier name |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

**Relationships:**
- `carrier_id` → `insurance_carriers.id`

### appeal_procedures
Insurance appeal processes and procedures.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `carrier_id` | bigint | YES | | Foreign key to insurance_carriers |
| `procedure_type` | varchar | NO | | Type of appeal procedure |
| `steps` | jsonb | YES | | Appeal steps |
| `timeframes` | jsonb | YES | | Time limits for appeals |
| `contact_info` | jsonb | YES | | Appeal contact information |
| `required_forms` | jsonb | YES | | Required forms and documents |
| `created_at` | timestamptz | YES | CURRENT_TIMESTAMP | Creation timestamp |
| `updated_at` | timestamptz | YES | CURRENT_TIMESTAMP | Last update timestamp |

**Relationships:**
- `carrier_id` → `insurance_carriers.id`

### documentation_requirements
Documentation requirements by carrier.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `carrier_id` | bigint | YES | | Foreign key to insurance_carriers |
| `document_type` | varchar | NO | | Type of document required |
| `description` | text | YES | | Description of requirement |
| `required_for` | jsonb | YES | | What this is required for |
| `format_requirements` | text | YES | | Format specifications |
| `category` | varchar | YES | | Category of requirement |
| `priority` | varchar | YES | medium | Priority level |
| `created_at` | timestamptz | YES | CURRENT_TIMESTAMP | Creation timestamp |
| `updated_at` | timestamptz | YES | CURRENT_TIMESTAMP | Last update timestamp |

**Relationships:**
- `carrier_id` → `insurance_carriers.id`

## Document Management

### ingested_documents
Tracks uploaded insurance documents.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `filename` | text | NO | | Original filename |
| `insurer` | text | YES | | Insurance company name |
| `procedure_code` | text | YES | | Related procedure code |
| `document_type` | text | NO | | Type of document |
| `upload_date` | timestamptz | YES | now() | Upload timestamp |
| `file_size` | bigint | YES | | File size in bytes |
| `file_path` | text | YES | | Storage file path |
| `processing_status` | text | YES | pending | Processing status |
| `storage_path` | text | YES | | Storage location |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

### document_chunks
Chunked content from ingested documents.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `document_id` | uuid | YES | | Foreign key to ingested_documents |
| `chunk_index` | integer | NO | | Chunk sequence number |
| `content` | text | NO | | Chunk content |
| `embedding` | vector | YES | | Vector embedding |
| `metadata` | jsonb | YES | '{}' | Chunk metadata |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

**Relationships:**
- `document_id` → `ingested_documents.id`

## Network and Plan Tables

### insurance_networks
Insurance network information.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `network_name` | text | NO | | Network name |
| `contact_phone` | text | YES | | Contact phone |
| `contact_email` | text | YES | | Contact email |
| `resource_url` | text | YES | | Network resource URL |
| `notes` | text | YES | | Additional notes |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

### insurance_plans
Insurance plan details.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `carrier_id` | bigint | YES | | Foreign key to insurance_carriers |
| `plan_name` | text | NO | | Plan name |
| `plan_type` | text | YES | | Type of plan |
| `is_medicare_advantage` | boolean | YES | false | Medicare Advantage flag |
| `notes` | text | YES | | Additional notes |
| `created_at` | timestamptz | YES | now() | Creation timestamp |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

**Relationships:**
- `carrier_id` → `insurance_carriers.id`

## Logging and Analytics

### search_logs
Tracks vector search queries and results.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | auto | Primary key |
| `query_text` | text | NO | | Search query text |
| `matched_content_ids` | jsonb | YES | | IDs of matched content |
| `similarity_scores` | jsonb | YES | | Similarity scores |
| `user_feedback` | text | YES | | User feedback on results |
| `created_at` | timestamptz | YES | CURRENT_TIMESTAMP | Creation timestamp |

### query_logs
General query logging.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | integer | NO | auto | Primary key |
| `query_text` | text | NO | | SQL query text |
| `query_type` | varchar | NO | | Type of query |
| `results_count` | integer | YES | | Number of results |
| `execution_time` | double precision | YES | | Execution time |
| `timestamp` | timestamp | YES | | Query timestamp |

## Database Functions

### search_guidelines_vector
Performs vector similarity search on guidelines.

**Parameters:**
- `query_embedding` (vector): Query embedding vector
- `content_type_filter` (text, optional): Filter by content type
- `carrier_id_filter` (bigint, optional): Filter by carrier ID
- `category_filter` (text, optional): Filter by category
- `similarity_threshold` (float, optional): Minimum similarity score
- `max_results` (integer, optional): Maximum results to return

**Returns:**
Array of results with similarity scores and metadata.

### search_guidelines_hybrid
Performs hybrid search combining vector similarity and recency.

**Parameters:**
- `query_embedding` (vector): Query embedding vector
- `carrier_id_filter` (bigint, optional): Filter by carrier ID
- `category_filter` (text, optional): Filter by category
- `similarity_threshold` (float, optional): Minimum similarity score
- `max_results` (integer, optional): Maximum results to return

**Returns:**
Array of results with similarity scores, recency scores, and hybrid scores.

## Indexes

### Performance Indexes
- `idx_embeddings_vector_cosine`: HNSW index for vector similarity search
- `idx_embeddings_metadata_gin`: GIN index for metadata queries
- `idx_guidelines_carrier_category`: Composite index for guideline lookups
- `idx_procedures_code`: Index on procedure codes
- `idx_carriers_name`: Index on carrier names

### Unused Indexes (Consider Removal)
- `idx_json_uploads_file_name`
- `idx_glossary_terms_term`
- `idx_glossary_terms_letter`
- `idx_document_chunks_embedding`
- `idx_ingested_documents_type`
- `idx_ingested_documents_insurer`