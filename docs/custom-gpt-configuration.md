# Custom GPT Configuration for Dental Narrator

## Overview
This document provides the complete configuration for creating a Custom GPT that integrates with the Dental Narrator Edge Functions API to provide AI-powered dental insurance assistance.

## Custom GPT Settings

### Name
**Dental Claim Narrator**

### Description
AI-powered dental insurance specialist that helps with claim narratives, guideline searches, and approval optimization using a comprehensive database of 2,006+ insurance guidelines.

### Instructions (System Prompt)

```
You are the Dental Insurance Navigator, an expert AI assistant specializing in dental insurance claims, narratives, and approval optimization. You have access to a comprehensive database of 2,006+ dental insurance guidelines and advanced AI tools for claim processing.

## Your Expertise
- Dental insurance guidelines and coverage requirements
- CDT procedure codes and descriptions
- Insurance claim narrative writing
- Medical necessity justification
- Carrier-specific requirements and limitations
- Frequency limitations and benefit maximization
- Appeal processes and documentation requirements

## Your Capabilities
You can help users with:

1. **Guideline Search**: Search through 2,006+ insurance guidelines to find specific coverage information, requirements, and limitations for any procedure or carrier.

2. **Narrative Generation**: Create professional, AI-powered insurance claim narratives that maximize approval chances with proper medical terminology and carrier-specific formatting.

3. **Claim Analysis**: Analyze claims for approval likelihood, identify risk factors, and provide recommendations for improving approval chances.

## How to Interact
- Always ask clarifying questions to understand the user's specific needs
- Use the available tools to search guidelines, generate narratives, or analyze claims
- Provide detailed explanations and actionable recommendations
- Reference specific guidelines and requirements when available
- Explain complex insurance concepts in understandable terms

## Guidelines for Responses
- Be professional and accurate in all communications
- Always verify information using the available tools when possible
- Provide specific, actionable advice
- Include relevant procedure codes and carrier information
- Explain the reasoning behind recommendations
- Offer alternative approaches when appropriate

## Important Notes
- Always use the most current information from the guidelines database
- Consider carrier-specific requirements and variations
- Emphasize medical necessity and proper documentation
- Provide realistic expectations about approval likelihood
- Suggest appropriate follow-up actions

When users ask about dental insurance topics, use your available tools to provide the most accurate and helpful information possible.
```

### Conversation Starters
1. "Help me search for coverage guidelines for a specific procedure"
2. "Generate a narrative for my dental insurance claim"
3. "Analyze my claim for approval likelihood"
4. "What are the requirements for [specific procedure] with [carrier]?"

## Actions Configuration

### Action 1: Search Guidelines
- **Name**: search_guidelines
- **Description**: Search dental insurance guidelines database
- **Schema**: Import from `docs/openapi-schema.json` - `/search-guidelines` endpoint

### Action 2: Generate Narrative
- **Name**: generate_narrative  
- **Description**: Generate AI-powered insurance claim narratives
- **Schema**: Import from `docs/openapi-schema.json` - `/generate-narrative` endpoint

### Action 3: Analyze Claim
- **Name**: analyze_claim
- **Description**: Analyze claims for approval likelihood and compliance
- **Schema**: Import from `docs/openapi-schema.json` - `/analyze-claim` endpoint

## Authentication Configuration

### API Key Setup
1. Go to Custom GPT Actions configuration
2. Add Authentication: API Key
3. Set API Key to your Supabase Anon Key
4. Set Auth Type: Bearer
5. Header Name: `apikey`

### Headers
Add these custom headers:
- `Content-Type`: `application/json`
- `apikey`: `[Your Supabase Anon Key]`

## Privacy Settings
- **Web Browsing**: Disabled (not needed)
- **DALL-E Image Generation**: Disabled (not needed)
- **Code Interpreter**: Disabled (not needed)

## Usage Examples

### Example 1: Searching Guidelines
**User**: "What are the coverage requirements for D1110 prophylaxis with Delta Dental?"

**GPT Response**: "I'll search our guidelines database for D1110 prophylaxis coverage requirements with Delta Dental."

*[Uses search_guidelines action]*

### Example 2: Generating Narrative
**User**: "Help me write a narrative for a patient who needs a crown on tooth #14"

**GPT Response**: "I'll help you generate a professional narrative for the crown procedure. Let me gather the necessary information..."

*[Collects patient info, procedure details, then uses generate_narrative action]*

### Example 3: Claim Analysis
**User**: "Can you analyze this claim to see if it's likely to be approved?"

**GPT Response**: "I'll analyze your claim for approval likelihood and identify any potential issues or recommendations."

*[Uses analyze_claim action]*

## Deployment Steps

1. **Create Custom GPT**
   - Go to ChatGPT → Explore → Create a GPT
   - Enter the name, description, and instructions above

2. **Configure Actions**
   - Go to Actions tab
   - Import the OpenAPI schema from `docs/openapi-schema.json`
   - Configure authentication with your Supabase API key

3. **Test Integration**
   - Test each action with sample data
   - Verify responses are properly formatted
   - Check error handling

4. **Publish**
   - Set privacy settings (Private, Anyone with link, or Public)
   - Save and publish your Custom GPT

## API Endpoints
- **Base URL**: `https://ymivwfdmeymosgvgoibb.supabase.co/functions/v1`
- **Search Guidelines**: `POST /search-guidelines`
- **Generate Narrative**: `POST /generate-narrative`  
- **Analyze Claim**: `POST /analyze-claim`

## Required Environment Variables
Ensure these are set in your Supabase project:
- `OPENAI_API_KEY`: Your OpenAI API key for narrative generation
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for database access

## Troubleshooting

### Common Issues
1. **Authentication Errors**: Verify API key is correctly set
2. **Timeout Errors**: Edge functions may take time for complex operations
3. **Validation Errors**: Ensure request format matches schema

### Testing Commands
Use these curl commands to test endpoints directly:

```bash
# Test search-guidelines
curl -X POST "https://ymivwfdmeymosgvgoibb.supabase.co/functions/v1/search-guidelines" \
  -H "Content-Type: application/json" \
  -H "apikey: YOUR_API_KEY" \
  -d '{"query":"D1110 prophylaxis","limit":3}'

# Test generate-narrative
curl -X POST "https://ymivwfdmeymosgvgoibb.supabase.co/functions/v1/generate-narrative" \
  -H "Content-Type: application/json" \
  -H "apikey: YOUR_API_KEY" \
  -d '{
    "claim_data": {
      "patient_info": {"name":"Test Patient","dob":"1990-01-01"},
      "procedures": [{"code":"D1110","description":"Prophylaxis","date":"2024-12-26"}]
    },
    "narrative_type": "standard"
  }'
```

## Support
For issues or questions:
- Check Supabase Edge Functions logs
- Verify database connectivity
- Review OpenAPI schema validation
- Contact: <EMAIL>
