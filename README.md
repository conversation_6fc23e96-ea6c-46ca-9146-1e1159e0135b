# Dental Narrator - Insurance Claim Narrative Generator

A specialized RAG-based system for generating dental insurance claim narratives from chart notes. This tool helps dental professionals create accurate, compliant narratives that meet specific insurance carrier requirements.

## Features

- **RAG-based Insurance Guidelines**: Vector database with semantic search for carrier-specific guidelines
- **Intelligent Text Processing**: Extracts key information from dental chart notes
- **Carrier-Specific Formatting**: Tailors narratives to meet requirements for different carriers
- **Claim Analysis & Risk Assessment**: Comprehensive analysis of approval likelihood and risk factors
- **Medical Necessity Justification**: Explicitly provides treatment justification
- **Attachment Recommendations**: Identifies required attachments based on procedure type
- **Multiple API Interfaces**: Both OpenAI Agents SDK and REST API endpoints
- **Supabase Edge Functions**: Serverless functions for scalable narrative generation
- **Comprehensive Carrier Database**: 20+ major insurance carriers supported

## Technical Architecture

The system uses a Retrieval-Augmented Generation (RAG) approach:

1. **Indexing Phase**: Insurance guidelines are chunked, embedded, and stored in a vector database
2. **Retrieval Phase**: 
   - Extract relevant information from chart notes
   - Query vector database to find relevant guidelines based on carrier and procedure
3. **Generation Phase**:
   - Combine chart information and guidelines in a well-structured prompt
   - Use an LLM to generate a compliant narrative
   - Post-process to ensure character count requirements are met

## Project Structure

```
dental-narrator/
├── src/                    # Main application source code
│   ├── agents/            # OpenAI Agents SDK integration
│   ├── api/               # REST API server and endpoints
│   └── utils/             # Database utilities and helpers
├── scripts/               # Utility scripts for data management
├── assets/                # CSV data files for import
├── supabase/             # Supabase configuration and Edge Functions
│   ├── functions/         # Serverless Edge Functions (Deno)
│   └── migrations/        # Database migrations
├── @Parsed/              # Insurance carrier data (JSON format)
└── web-bundles/          # BMAD methodology and team configurations
```

### Scripts Directory

The `scripts/` folder contains all utility scripts for data management:
- **Data Import**: CSV import and database seeding
- **Data Quality**: Validation and cleaning scripts
- **AI Features**: Embedding generation and testing

See [scripts/README.md](scripts/README.md) for detailed documentation.

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL (for vector storage)
- OpenAI API key

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```
# Create a .env file with:
OPENAI_API_KEY=your_openai_api_key
POSTGRES_CONNECTION_STRING=postgresql://postgres:postgres@localhost:5432/postgres
```

4. Initialize the database:

```bash
npm run init
```

## Usage

### Available Interfaces

The system provides multiple ways to access its functionality:

1. **OpenAI Agents SDK** - Programmatic access
2. **REST API** - HTTP endpoints for integration
3. **Supabase Edge Functions** - Serverless functions

### Testing the System

```bash
# Test OpenAI Agents SDK
npm run test-openai-agent

# Test complete system integration
npm run test-system

# Test REST API endpoints
npm run test-api-endpoints

# Test frontend simulation
npm run test-frontend
```

### Starting the Development Server

```bash
npm run dev
```

This starts the Express.js server with auto-reload on port 3000 (configurable via PORT env var).

### REST API Endpoints

The system provides these main endpoints:

- `POST /search/guidelines` - Search insurance guidelines
- `POST /search/procedures` - Search dental procedures and CDT codes
- `POST /validate/coverage` - Validate insurance coverage for procedures
- `POST /glossary/lookup` - Look up dental terminology
- `GET /api/carriers` - Retrieve insurance carriers
- `GET /api/procedures` - Retrieve dental procedures
- `GET /api/appeals` - Retrieve appeal procedures

### Supabase Edge Functions

- `generate-narrative` - AI-powered narrative generation
- `analyze-claim` - Comprehensive claim analysis
- `search-guidelines` - Semantic guideline search

### Using the OpenAI Agent

The dental narrator agent accepts natural language queries such as:
"Generate a narrative for these chart notes for Delta Dental. The procedure is a root canal."

### Example Chart Notes

Here's a sample chart note you can use to test the system:

```
Patient: Jane Doe
Date: 2023-05-15
Chief Complaint: Patient reports severe pain in lower right quadrant, specifically tooth #30. Pain is spontaneous, throbbing, and wakes patient at night. Pain intensifies with hot beverages.

Clinical Findings:
- Visual exam shows large occlusal restoration with distal marginal breakdown
- Tooth #30 is tender to percussion
- Cold test: prolonged, lingering pain
- EPT: hypersensitive response at 4/10
- Radiographic exam: periapical radiolucency approximately 3mm in diameter

Diagnosis: Irreversible pulpitis with symptomatic apical periodontitis on tooth #30

Treatment: Root canal therapy recommended and performed on tooth #30
- Local anesthesia administered (2% lidocaine with 1:100,000 epinephrine)
- Rubber dam isolation
- Access preparation
- Working length determined with apex locator and confirmed radiographically
- Canals instrumented with rotary files
- Irrigation protocol: 5.25% NaOCl, 17% EDTA
- Canals obturated with gutta percha using warm vertical condensation
- Temporary restoration placed with Cavit

Patient tolerated procedure well. Postoperative instructions provided verbally and in writing. Patient scheduled for crown preparation in 2 weeks.

Medical History:
- Controlled hypertension (medication: lisinopril 10mg daily)
- Penicillin allergy (rash)
```

### Sample Output

The system returns:

- **Narrative**: The generated insurance claim narrative
- **Supporting Guidelines**: Relevant insurance guidelines consulted
- **Recommendations**: Specific recommendations for claim optimization
- **Confidence Score**: AI-calculated confidence in narrative quality
- **Risk Assessment**: Analysis of approval likelihood and potential issues
- **Required Attachments**: List of attachments needed for the claim

## Development

To extend or modify the system:

1. **Add Insurance Carriers**: Add JSON files to `@Parsed/` directory and run `npm run seed-database`
2. **Modify Narrative Logic**: 
   - OpenAI Agents: Update `src/agents/dental-narrator-agent.ts`
   - Edge Functions: Update `supabase/functions/generate-narrative/index.ts`
3. **Add API Endpoints**: Extend `src/api/data-endpoints.ts` with new routes
4. **Database Changes**: Add migrations to `supabase/migrations/`
5. **Test Changes**: Use comprehensive test suite with `npm run test-system`

For detailed development guidance, see [CLAUDE.md](CLAUDE.md).

## License

[MIT License](LICENSE)

## Acknowledgements

This project was built using the [OpenAI Agents SDK](https://github.com/openai/agents-sdk) framework.