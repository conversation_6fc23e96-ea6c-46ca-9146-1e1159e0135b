import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { EdgeDatabaseUtils } from '../_shared/database/index.ts';
import { GenerateNarrativeSchema, validateRequestBody } from '../_shared/validation/index.ts';
import { ResponseBuilder, parseRequestBody, logRequest, logResponse } from '../_shared/responses/index.ts';
import { withErrorHandling } from '../_shared/errors/index.ts';
import { getConfig, handleCorsPreflightRequest, getLogger } from '../_shared/config/index.ts';
import type { GenerateNarrativeRequest, GenerateNarrativeResponse } from '../_shared/types/index.ts';

const config = getConfig();
const logger = getLogger();
const dbUtils = EdgeDatabaseUtils.getInstance();

/**
 * Generate dental insurance narrative using AI and guidelines database
 */
async function generateNarrativeHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  logger.logRequest(request);

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return handleCorsPreflightRequest(request, config);
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // Parse and validate request body
  const body = await parseRequestBody(request);
  const validatedData = validateRequestBody(GenerateNarrativeSchema, body);
  const { claim_data, carrier, narrative_type } = validatedData;

  logger.info('Generating narrative', {
    carrier,
    narrative_type,
    procedures_count: claim_data.procedures.length,
    patient_name: claim_data.patient_info.name
  });

  // Search for relevant guidelines based on procedures and diagnosis
  const searchQueries = [
    // Primary search based on procedures
    ...claim_data.procedures.map(proc => `${proc.code} ${proc.description}`),
    // Secondary search based on diagnosis if provided
    ...(claim_data.diagnosis ? [claim_data.diagnosis] : []),
    // Tertiary search for narrative type specific guidelines
    `${narrative_type} requirements documentation`
  ];

  const allGuidelines = [];
  for (const query of searchQueries) {
    try {
      const guidelines = await dbUtils.searchGuidelines(query, {
        limit: 3,
        similarityThreshold: 0.3,
        carrierFilter: carrier
      });
      allGuidelines.push(...guidelines);
    } catch (error) {
      logger.warn(`Failed to search guidelines for query: ${query}`, error);
    }
  }

  // Remove duplicates and sort by relevance
  const uniqueGuidelines = Array.from(
    new Map(allGuidelines.map(g => [g.id, g])).values()
  ).sort((a, b) => b.similarity_score - a.similarity_score);

  // Generate narrative using OpenAI
  const narrative = await generateNarrativeWithAI(
    claim_data,
    uniqueGuidelines,
    narrative_type,
    carrier
  );

  // Generate recommendations based on guidelines
  const recommendations = generateRecommendations(
    claim_data,
    uniqueGuidelines,
    narrative_type
  );

  // Calculate confidence score based on guidelines found and relevance
  const confidenceScore = calculateConfidenceScore(
    uniqueGuidelines,
    claim_data.procedures.length
  );

  const duration = Date.now() - startTime;
  logResponse('POST', '/generate-narrative', 200, duration);

  const responseData: GenerateNarrativeResponse['data'] = {
    narrative,
    supporting_guidelines: uniqueGuidelines.slice(0, 5), // Top 5 most relevant
    recommendations,
    confidence_score: confidenceScore
  };

  return ResponseBuilder.success(responseData, {
    processing_time_ms: duration,
    guidelines_found: uniqueGuidelines.length,
    narrative_type,
    carrier: carrier || 'generic'
  });
}

/**
 * Generate narrative using OpenAI API
 */
async function generateNarrativeWithAI(
  claimData: any,
  guidelines: any[],
  narrativeType: string,
  carrier?: string
): Promise<string> {
  const systemPrompt = `You are a dental insurance narrative specialist. Generate a professional, detailed narrative for ${narrativeType} submissions that maximizes approval chances.

Key Requirements:
- Use medical terminology appropriately
- Reference relevant insurance guidelines
- Justify medical necessity clearly
- Include specific clinical details
- Follow ${carrier || 'standard'} formatting requirements

Guidelines Context:
${guidelines.map(g => `- ${g.title}: ${g.content.substring(0, 200)}...`).join('\n')}`;

  const userPrompt = `Generate a ${narrativeType} narrative for:

Patient: ${claimData.patient_info.name} (DOB: ${claimData.patient_info.dob})
${claimData.patient_info.member_id ? `Member ID: ${claimData.patient_info.member_id}` : ''}

Procedures:
${claimData.procedures.map(proc => 
  `- ${proc.code}: ${proc.description} (Date: ${proc.date})${proc.tooth ? ` - Tooth #${proc.tooth}` : ''}${proc.surfaces ? ` - Surfaces: ${proc.surfaces.join(', ')}` : ''}`
).join('\n')}

${claimData.diagnosis ? `Diagnosis: ${claimData.diagnosis}` : ''}
${claimData.medical_history ? `Medical History: ${claimData.medical_history}` : ''}
${claimData.treatment_plan ? `Treatment Plan: ${claimData.treatment_plan}` : ''}

Generate a comprehensive narrative that addresses medical necessity and follows insurance requirements.`;

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.openai.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: config.openai.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 1500,
        temperature: 0.3
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'Unable to generate narrative at this time.';
  } catch (error) {
    logger.error('Failed to generate narrative with AI', error);
    return generateFallbackNarrative(claimData, narrativeType);
  }
}

/**
 * Generate fallback narrative without AI
 */
function generateFallbackNarrative(claimData: any, narrativeType: string): string {
  const procedures = claimData.procedures.map(proc => 
    `${proc.code} (${proc.description}) performed on ${proc.date}${proc.tooth ? ` on tooth #${proc.tooth}` : ''}`
  ).join('; ');

  return `${narrativeType.charAt(0).toUpperCase() + narrativeType.slice(1)} narrative for patient ${claimData.patient_info.name}:

The following procedures were performed: ${procedures}.

${claimData.diagnosis ? `Clinical diagnosis: ${claimData.diagnosis}. ` : ''}
${claimData.medical_history ? `Relevant medical history: ${claimData.medical_history}. ` : ''}

The treatment was medically necessary and performed according to standard dental care protocols. All procedures were completed with appropriate clinical documentation and follow established treatment guidelines.

${claimData.treatment_plan ? `Treatment plan: ${claimData.treatment_plan}` : ''}`;
}

/**
 * Generate recommendations based on guidelines and claim data
 */
function generateRecommendations(
  claimData: any,
  guidelines: any[],
  narrativeType: string
): string[] {
  const recommendations = [];

  // Basic recommendations
  recommendations.push('Ensure all clinical documentation is complete and legible');
  recommendations.push('Include pre-treatment and post-treatment radiographs if applicable');

  // Narrative type specific recommendations
  if (narrativeType === 'appeal') {
    recommendations.push('Address the specific denial reason in your appeal letter');
    recommendations.push('Provide additional clinical justification for medical necessity');
  } else if (narrativeType === 'predetermination') {
    recommendations.push('Submit treatment plan with estimated fees');
    recommendations.push('Include patient\'s current oral health status');
  }

  // Guideline-based recommendations
  if (guidelines.length > 0) {
    recommendations.push('Review carrier-specific guidelines for additional requirements');
    if (guidelines.some(g => g.content.toLowerCase().includes('frequency'))) {
      recommendations.push('Verify procedure frequency limitations with carrier');
    }
    if (guidelines.some(g => g.content.toLowerCase().includes('age'))) {
      recommendations.push('Consider age-related coverage limitations');
    }
  }

  // Procedure-specific recommendations
  const hasSurgicalProcedures = claimData.procedures.some((proc: any) => 
    proc.code.startsWith('D7') || proc.description.toLowerCase().includes('surgery')
  );
  if (hasSurgicalProcedures) {
    recommendations.push('Include detailed surgical notes and post-operative instructions');
  }

  return recommendations.slice(0, 6); // Limit to 6 recommendations
}

/**
 * Calculate confidence score based on available guidelines and data completeness
 */
function calculateConfidenceScore(guidelines: any[], procedureCount: number): number {
  let score = 0.5; // Base score

  // Guidelines factor (0-0.3)
  if (guidelines.length > 0) {
    const avgSimilarity = guidelines.reduce((sum, g) => sum + g.similarity_score, 0) / guidelines.length;
    score += Math.min(avgSimilarity * 0.3, 0.3);
  }

  // Procedure coverage factor (0-0.2)
  const coveredProcedures = guidelines.filter(g => 
    g.content.toLowerCase().includes('covered') || 
    g.content.toLowerCase().includes('benefit')
  ).length;
  score += Math.min((coveredProcedures / procedureCount) * 0.2, 0.2);

  return Math.min(Math.max(score, 0), 1); // Clamp between 0 and 1
}

// Serve the function
serve(withErrorHandling(generateNarrativeHandler, 'generate-narrative'));
