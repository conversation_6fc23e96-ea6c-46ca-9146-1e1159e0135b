/**
 * Security middleware and utilities for Edge Functions
 */

import { ResponseBuilder } from '../responses/index.ts';
import { getConfig, checkRateLimit } from '../config/index.ts';

/**
 * Rate limiting store (in-memory for simplicity)
 */
class RateLimitStore {
  private requests: Map<string, { count: number; resetTime: number }> = new Map();
  private readonly windowMs = 60000; // 1 minute window
  private readonly maxRequests = 100; // Max requests per window

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const record = this.requests.get(identifier);

    if (!record || now > record.resetTime) {
      // New window or expired window
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs
      });
      return true;
    }

    if (record.count >= this.maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

const rateLimitStore = new RateLimitStore();

// Cleanup expired entries every 5 minutes
setInterval(() => rateLimitStore.cleanup(), 5 * 60 * 1000);

/**
 * Security middleware for Edge Functions
 */
export class SecurityMiddleware {
  /**
   * Apply security checks to incoming requests
   */
  static async applySecurityChecks(request: Request): Promise<Response | null> {
    // Rate limiting
    const rateLimitResult = this.checkRateLimit(request);
    if (rateLimitResult) return rateLimitResult;

    // API key validation
    const authResult = this.validateAuthentication(request);
    if (authResult) return authResult;

    // Input validation and sanitization
    const inputResult = await this.validateInput(request);
    if (inputResult) return inputResult;

    // CORS validation
    const corsResult = this.validateCors(request);
    if (corsResult) return corsResult;

    return null; // All checks passed
  }

  /**
   * Check rate limiting
   */
  private static checkRateLimit(request: Request): Response | null {
    const identifier = this.getClientIdentifier(request);
    
    if (!rateLimitStore.isAllowed(identifier)) {
      return ResponseBuilder.rateLimitExceeded(
        'Too many requests. Please try again later.'
      );
    }

    return null;
  }

  /**
   * Validate API key authentication
   */
  private static validateAuthentication(request: Request): Response | null {
    const config = getConfig();
    const apiKey = request.headers.get('apikey') || 
                   request.headers.get('authorization')?.replace('Bearer ', '');

    if (!apiKey) {
      return ResponseBuilder.unauthorized('API key required');
    }

    // Validate against known keys
    const validKeys = [config.supabase.anonKey, config.supabase.serviceRoleKey];
    if (!validKeys.includes(apiKey)) {
      return ResponseBuilder.unauthorized('Invalid API key');
    }

    return null;
  }

  /**
   * Validate and sanitize input
   */
  private static async validateInput(request: Request): Promise<Response | null> {
    // Skip validation for OPTIONS requests
    if (request.method === 'OPTIONS') {
      return null;
    }

    // Check content type for POST requests
    if (request.method === 'POST') {
      const contentType = request.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        return ResponseBuilder.validationError(
          'Content-Type must be application/json'
        );
      }

      // Check content length
      const contentLength = request.headers.get('content-length');
      if (contentLength && parseInt(contentLength) > 1024 * 1024) { // 1MB limit
        return ResponseBuilder.validationError(
          'Request body too large. Maximum size is 1MB.'
        );
      }
    }

    return null;
  }

  /**
   * Validate CORS
   */
  private static validateCors(request: Request): Response | null {
    const origin = request.headers.get('origin');
    
    // For development, allow localhost
    if (origin?.includes('localhost') || origin?.includes('127.0.0.1')) {
      return null;
    }

    // Add production domain validation here if needed
    return null;
  }

  /**
   * Get client identifier for rate limiting
   */
  private static getClientIdentifier(request: Request): string {
    // Try to get real IP from headers (when behind proxy)
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    
    return forwardedFor?.split(',')[0] || 
           realIp || 
           cfConnectingIp || 
           'unknown';
  }
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize string input to prevent injection attacks
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[;]/g, '') // Remove semicolons
      .trim()
      .substring(0, 1000); // Limit length
  }

  /**
   * Sanitize procedure code
   */
  static sanitizeProcedureCode(code: string): string {
    if (typeof code !== 'string') {
      return '';
    }

    // CDT codes should be D followed by 4 digits
    const sanitized = code.toUpperCase().replace(/[^D0-9]/g, '');
    return sanitized.match(/^D\d{4}$/) ? sanitized : '';
  }

  /**
   * Sanitize date input
   */
  static sanitizeDate(date: string): string {
    if (typeof date !== 'string') {
      return '';
    }

    // Validate YYYY-MM-DD format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return '';
    }

    // Validate actual date
    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      return '';
    }

    return date;
  }

  /**
   * Sanitize carrier name
   */
  static sanitizeCarrierName(name: string): string {
    if (typeof name !== 'string') {
      return '';
    }

    return name
      .replace(/[^a-zA-Z0-9\s\-\.]/g, '') // Allow only alphanumeric, spaces, hyphens, dots
      .trim()
      .substring(0, 100);
  }
}

/**
 * Security headers utility
 */
export class SecurityHeaders {
  /**
   * Get security headers for responses
   */
  static getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': "default-src 'self'; script-src 'none'; object-src 'none';",
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
    };
  }

  /**
   * Add security headers to response
   */
  static addSecurityHeaders(response: Response): Response {
    const securityHeaders = this.getSecurityHeaders();
    
    // Clone response to add headers
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        ...securityHeaders
      }
    });

    return newResponse;
  }
}

/**
 * Request validation utilities
 */
export class RequestValidator {
  /**
   * Validate request size
   */
  static validateRequestSize(request: Request): boolean {
    const contentLength = request.headers.get('content-length');
    if (contentLength) {
      const size = parseInt(contentLength);
      return size <= 1024 * 1024; // 1MB limit
    }
    return true;
  }

  /**
   * Validate request method
   */
  static validateMethod(request: Request, allowedMethods: string[]): boolean {
    return allowedMethods.includes(request.method);
  }

  /**
   * Validate content type
   */
  static validateContentType(request: Request): boolean {
    if (request.method === 'POST' || request.method === 'PUT') {
      const contentType = request.headers.get('content-type');
      return contentType?.includes('application/json') || false;
    }
    return true;
  }
}

/**
 * Security wrapper for Edge Functions
 */
export function withSecurity(
  handler: (request: Request) => Promise<Response>,
  options: {
    allowedMethods?: string[];
    requireAuth?: boolean;
    rateLimitEnabled?: boolean;
  } = {}
): (request: Request) => Promise<Response> {
  const {
    allowedMethods = ['GET', 'POST', 'OPTIONS'],
    requireAuth = true,
    rateLimitEnabled = true
  } = options;

  return async (request: Request): Promise<Response> => {
    try {
      // Method validation
      if (!RequestValidator.validateMethod(request, allowedMethods)) {
        return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
      }

      // Apply security checks
      if (requireAuth || rateLimitEnabled) {
        const securityResult = await SecurityMiddleware.applySecurityChecks(request);
        if (securityResult) {
          return SecurityHeaders.addSecurityHeaders(securityResult);
        }
      }

      // Call the actual handler
      const response = await handler(request);

      // Add security headers to response
      return SecurityHeaders.addSecurityHeaders(response);

    } catch (error) {
      console.error('Security wrapper error:', error);
      const errorResponse = ResponseBuilder.error(
        'Internal server error',
        500,
        'INTERNAL_ERROR'
      );
      return SecurityHeaders.addSecurityHeaders(errorResponse);
    }
  };
}
