// Shared TypeScript types for Edge Functions

export interface VectorSearchResult {
  id: number;
  content_type: string;
  content_id: number;
  similarity_score: number;
  metadata?: Record<string, any>;
  content?: string;
}

export interface GuidelineSearchResult {
  id: number;
  title: string;
  category: string;
  carrier: string;
  similarity_score: number;
  content: string;
}

export interface CarrierInfo {
  id: number;
  name: string;
  type?: string;
  payer_id?: string;
  claims_address?: string;
  phone_number?: string;
  website?: string;
  contact_info?: Record<string, any>;
}

export interface ProcedureInfo {
  id: number;
  cdt_code: string;
  name: string;
  description: string;
  category?: string;
  found: boolean;
}

export interface DatabaseStats {
  totalGuidelines: number;
  totalEmbeddings: number;
  totalCarriers: number;
  vectorExtensionEnabled: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  metadata?: {
    timestamp: string;
    [key: string]: any;
  };
}

export interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

export interface SearchOptions {
  limit?: number;
  similarityThreshold?: number;
  carrierFilter?: string;
  categoryFilter?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// Request/Response types for specific endpoints
export interface SearchGuidelinesRequest {
  query: string;
  carrier?: string;
  category?: string;
  limit?: number;
}

export interface SearchGuidelinesResponse extends ApiResponse {
  data: {
    results: GuidelineSearchResult[];
    total_found: number;
    search_metadata: {
      query: string;
      carrier_filter?: string;
      category_filter?: string;
      similarity_threshold: number;
    };
  };
}

export interface ValidateCoverageRequest {
  carrier: string;
  procedure_codes: string[];
  patient_info?: {
    age?: number;
    member_id?: string;
  };
}

export interface ValidateCoverageResponse extends ApiResponse {
  data: {
    carrier: CarrierInfo;
    procedures: ProcedureInfo[];
    coverage_guidelines: GuidelineSearchResult[];
    validation_summary: {
      carrier_found: boolean;
      procedures_found: number;
      total_procedures: number;
      guidelines_found: number;
    };
  };
}

export interface GenerateNarrativeRequest {
  claim_data: {
    patient_info: {
      name: string;
      dob: string;
      member_id?: string;
    };
    procedures: Array<{
      code: string;
      description: string;
      date: string;
      tooth?: string;
      surfaces?: string[];
    }>;
    diagnosis?: string;
    medical_history?: string;
    treatment_plan?: string;
  };
  carrier?: string;
  narrative_type?: 'standard' | 'appeal' | 'predetermination';
}

export interface GenerateNarrativeResponse extends ApiResponse {
  data: {
    narrative: string;
    supporting_guidelines: GuidelineSearchResult[];
    recommendations: string[];
    confidence_score: number;
  };
}
