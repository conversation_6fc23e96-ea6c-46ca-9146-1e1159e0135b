import { ResponseBuilder, logError } from '../responses/index.ts';

/**
 * Custom error classes for Edge Functions
 */

export class EdgeFunctionError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly details?: any;

  constructor(
    message: string,
    code: string = 'EDGE_FUNCTION_ERROR',
    statusCode: number = 500,
    details?: any
  ) {
    super(message);
    this.name = 'EdgeFunctionError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

export class ValidationError extends EdgeFunctionError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

export class DatabaseError extends EdgeFunctionError {
  constructor(message: string, details?: any) {
    super(message, 'DATABASE_ERROR', 500, details);
    this.name = 'DatabaseError';
  }
}

export class NotFoundError extends EdgeFunctionError {
  constructor(resource: string) {
    super(`${resource} not found`, 'NOT_FOUND', 404);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends EdgeFunctionError {
  constructor(message: string = 'Unauthorized access') {
    super(message, 'UNAUTHORIZED', 401);
    this.name = 'UnauthorizedError';
  }
}

export class RateLimitError extends EdgeFunctionError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_EXCEEDED', 429);
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends EdgeFunctionError {
  constructor(service: string, message: string, details?: any) {
    super(`${service} service error: ${message}`, 'EXTERNAL_SERVICE_ERROR', 503, details);
    this.name = 'ExternalServiceError';
  }
}

/**
 * Error handling middleware for Edge Functions
 */

export class ErrorHandler {
  /**
   * Main error handling function
   */
  static handle(
    error: any,
    request: Request,
    functionName: string
  ): Response {
    const method = request.method;
    const url = new URL(request.url);
    const path = url.pathname;

    // Log the error
    logError(method, path, error, {
      function: functionName,
      userAgent: request.headers.get('user-agent'),
      origin: request.headers.get('origin')
    });

    // Handle known error types
    if (error instanceof EdgeFunctionError) {
      return ResponseBuilder.error(
        error.message,
        error.statusCode,
        error.code,
        error.details
      );
    }

    // Handle Zod validation errors
    if (error.name === 'ZodError') {
      return this.handleZodError(error);
    }

    // Handle database-specific errors
    if (this.isDatabaseError(error)) {
      return this.handleDatabaseError(error);
    }

    // Handle OpenAI API errors
    if (this.isOpenAIError(error)) {
      return this.handleOpenAIError(error);
    }

    // Handle network/fetch errors
    if (this.isNetworkError(error)) {
      return this.handleNetworkError(error);
    }

    // Handle JSON parsing errors
    if (this.isJSONError(error)) {
      return ResponseBuilder.validationError(
        'Invalid JSON in request body',
        { originalError: error.message }
      );
    }

    // Generic error fallback
    return ResponseBuilder.error(
      'An unexpected error occurred',
      500,
      'INTERNAL_ERROR',
      { originalError: error.message }
    );
  }

  /**
   * Handle Zod validation errors
   */
  private static handleZodError(error: any): Response {
    const validationErrors = error.errors?.map((err: any) => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    })) || [];

    return ResponseBuilder.validationError(
      'Request validation failed',
      { validationErrors }
    );
  }

  /**
   * Handle database errors
   */
  private static handleDatabaseError(error: any): Response {
    const message = error.message?.toLowerCase() || '';

    if (message.includes('connection')) {
      return ResponseBuilder.error(
        'Database connection error',
        503,
        'DATABASE_CONNECTION_ERROR'
      );
    }

    if (message.includes('timeout')) {
      return ResponseBuilder.error(
        'Database operation timed out',
        504,
        'DATABASE_TIMEOUT'
      );
    }

    if (message.includes('constraint') || message.includes('duplicate')) {
      return ResponseBuilder.error(
        'Data constraint violation',
        409,
        'DATABASE_CONSTRAINT_ERROR'
      );
    }

    if (message.includes('not found') || message.includes('no rows')) {
      return ResponseBuilder.notFound('Requested resource');
    }

    return ResponseBuilder.error(
      'Database operation failed',
      500,
      'DATABASE_ERROR',
      { originalError: error.message }
    );
  }

  /**
   * Handle OpenAI API errors
   */
  private static handleOpenAIError(error: any): Response {
    const message = error.message?.toLowerCase() || '';

    if (message.includes('api key') || message.includes('unauthorized')) {
      return ResponseBuilder.error(
        'AI service configuration error',
        503,
        'AI_SERVICE_CONFIG_ERROR'
      );
    }

    if (message.includes('rate limit') || message.includes('quota')) {
      return ResponseBuilder.rateLimitExceeded(
        'AI service rate limit exceeded'
      );
    }

    if (message.includes('model') || message.includes('invalid')) {
      return ResponseBuilder.error(
        'AI service request error',
        400,
        'AI_SERVICE_REQUEST_ERROR',
        { originalError: error.message }
      );
    }

    return ResponseBuilder.error(
      'AI service temporarily unavailable',
      503,
      'AI_SERVICE_ERROR',
      { originalError: error.message }
    );
  }

  /**
   * Handle network/fetch errors
   */
  private static handleNetworkError(error: any): Response {
    return ResponseBuilder.error(
      'External service unavailable',
      503,
      'NETWORK_ERROR',
      { originalError: error.message }
    );
  }

  /**
   * Error type detection utilities
   */
  private static isDatabaseError(error: any): boolean {
    const message = error.message?.toLowerCase() || '';
    const errorTypes = [
      'connection', 'timeout', 'constraint', 'duplicate',
      'not found', 'no rows', 'database', 'postgres', 'supabase'
    ];
    return errorTypes.some(type => message.includes(type));
  }

  private static isOpenAIError(error: any): boolean {
    const message = error.message?.toLowerCase() || '';
    return message.includes('openai') || 
           message.includes('api key') ||
           message.includes('embedding') ||
           error.name === 'OpenAIError';
  }

  private static isNetworkError(error: any): boolean {
    return error.name === 'TypeError' && 
           (error.message?.includes('fetch') || error.message?.includes('network'));
  }

  private static isJSONError(error: any): boolean {
    return error.name === 'SyntaxError' && 
           error.message?.includes('JSON');
  }
}

/**
 * Async error wrapper for Edge Functions
 */
export function withErrorHandling(
  handler: (request: Request) => Promise<Response>,
  functionName: string
): (request: Request) => Promise<Response> {
  return async (request: Request): Promise<Response> => {
    try {
      return await handler(request);
    } catch (error) {
      return ErrorHandler.handle(error, request, functionName);
    }
  };
}

/**
 * Utility functions for throwing specific errors
 */

export function throwValidationError(message: string, details?: any): never {
  throw new ValidationError(message, details);
}

export function throwNotFoundError(resource: string): never {
  throw new NotFoundError(resource);
}

export function throwUnauthorizedError(message?: string): never {
  throw new UnauthorizedError(message);
}

export function throwDatabaseError(message: string, details?: any): never {
  throw new DatabaseError(message, details);
}

export function throwRateLimitError(message?: string): never {
  throw new RateLimitError(message);
}

export function throwExternalServiceError(service: string, message: string, details?: any): never {
  throw new ExternalServiceError(service, message, details);
}

/**
 * Error assertion utilities
 */

export function assertExists<T>(
  value: T | null | undefined,
  resource: string
): asserts value is T {
  if (value === null || value === undefined) {
    throwNotFoundError(resource);
  }
}

export function assertValidInput(
  condition: boolean,
  message: string,
  details?: any
): asserts condition {
  if (!condition) {
    throwValidationError(message, details);
  }
}

export function assertAuthorized(
  condition: boolean,
  message?: string
): asserts condition {
  if (!condition) {
    throwUnauthorizedError(message);
  }
}
