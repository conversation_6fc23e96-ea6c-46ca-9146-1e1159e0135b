import type { ApiResponse, PaginationInfo } from '../types/index.ts';

/**
 * Standardized response utilities for Edge Functions
 * Ensures consistent API response format across all endpoints
 */

export class ResponseBuilder {
  /**
   * Create a successful response
   */
  static success<T>(
    data: T,
    metadata?: Record<string, any>,
    status: number = 200
  ): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata
      }
    };

    return new Response(JSON.stringify(response), {
      status,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE'
      }
    });
  }

  /**
   * Create an error response
   */
  static error(
    message: string,
    status: number = 500,
    errorCode?: string,
    details?: any
  ): Response {
    const response: ApiResponse = {
      success: false,
      error: errorCode || 'INTERNAL_ERROR',
      message,
      metadata: {
        timestamp: new Date().toISOString(),
        ...(details && { details })
      }
    };

    return new Response(JSON.stringify(response), {
      status,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE'
      }
    });
  }

  /**
   * Create a validation error response
   */
  static validationError(
    message: string,
    validationDetails?: any
  ): Response {
    return this.error(
      message,
      400,
      'VALIDATION_ERROR',
      validationDetails
    );
  }

  /**
   * Create a not found response
   */
  static notFound(resource: string): Response {
    return this.error(
      `${resource} not found`,
      404,
      'NOT_FOUND'
    );
  }

  /**
   * Create an unauthorized response
   */
  static unauthorized(message: string = 'Unauthorized access'): Response {
    return this.error(
      message,
      401,
      'UNAUTHORIZED'
    );
  }

  /**
   * Create a rate limit exceeded response
   */
  static rateLimitExceeded(message: string = 'Rate limit exceeded'): Response {
    return this.error(
      message,
      429,
      'RATE_LIMIT_EXCEEDED'
    );
  }

  /**
   * Create a paginated response
   */
  static paginated<T>(
    results: T[],
    pagination: PaginationInfo,
    metadata?: Record<string, any>
  ): Response {
    const data = {
      results,
      pagination
    };

    return this.success(data, metadata);
  }

  /**
   * Handle CORS preflight requests
   */
  static cors(): Response {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE'
      }
    });
  }
}

/**
 * Utility functions for common response patterns
 */

export function createSearchResponse<T>(
  results: T[],
  query: string,
  totalFound: number,
  searchMetadata?: Record<string, any>
): Response {
  return ResponseBuilder.success({
    results,
    total_found: totalFound,
    search_metadata: {
      query,
      results_count: results.length,
      ...searchMetadata
    }
  });
}

export function createHealthCheckResponse(
  serviceName: string,
  additionalInfo?: Record<string, any>
): Response {
  return ResponseBuilder.success({
    status: 'healthy',
    service: serviceName,
    version: '1.0.0',
    ...additionalInfo
  });
}

export function createValidationSummaryResponse(
  summary: Record<string, any>,
  results: any[],
  metadata?: Record<string, any>
): Response {
  return ResponseBuilder.success({
    validation_summary: summary,
    results,
    metadata: {
      total_items: results.length,
      ...metadata
    }
  });
}

/**
 * Error handling utilities
 */

export function handleDatabaseError(error: any): Response {
  console.error('Database error:', error);
  
  // Handle specific database errors
  if (error.message?.includes('connection')) {
    return ResponseBuilder.error(
      'Database connection error',
      503,
      'DATABASE_CONNECTION_ERROR'
    );
  }
  
  if (error.message?.includes('timeout')) {
    return ResponseBuilder.error(
      'Database operation timed out',
      504,
      'DATABASE_TIMEOUT'
    );
  }
  
  if (error.message?.includes('not found')) {
    return ResponseBuilder.notFound('Requested resource');
  }
  
  // Generic database error
  return ResponseBuilder.error(
    'Database operation failed',
    500,
    'DATABASE_ERROR',
    { originalError: error.message }
  );
}

export function handleValidationError(error: any): Response {
  console.error('Validation error:', error);
  
  if (error.message?.includes('Validation error:')) {
    return ResponseBuilder.validationError(
      error.message,
      { validationErrors: error.message }
    );
  }
  
  return ResponseBuilder.validationError(
    'Request validation failed',
    { originalError: error.message }
  );
}

export function handleOpenAIError(error: any): Response {
  console.error('OpenAI API error:', error);
  
  if (error.message?.includes('API key')) {
    return ResponseBuilder.error(
      'AI service configuration error',
      503,
      'AI_SERVICE_CONFIG_ERROR'
    );
  }
  
  if (error.message?.includes('rate limit')) {
    return ResponseBuilder.rateLimitExceeded(
      'AI service rate limit exceeded'
    );
  }
  
  return ResponseBuilder.error(
    'AI service temporarily unavailable',
    503,
    'AI_SERVICE_ERROR',
    { originalError: error.message }
  );
}

/**
 * Request parsing utilities
 */

export async function parseRequestBody(request: Request): Promise<any> {
  try {
    const contentType = request.headers.get('content-type');
    
    if (!contentType?.includes('application/json')) {
      throw new Error('Content-Type must be application/json');
    }
    
    const body = await request.json();
    
    if (!body || typeof body !== 'object') {
      throw new Error('Request body must be a valid JSON object');
    }
    
    return body;
  } catch (error) {
    throw new Error(`Failed to parse request body: ${error.message}`);
  }
}

export function parseQueryParams(url: URL): Record<string, string> {
  const params: Record<string, string> = {};
  
  for (const [key, value] of url.searchParams.entries()) {
    params[key] = value;
  }
  
  return params;
}

/**
 * Logging utilities
 */

export function logRequest(
  method: string,
  path: string,
  metadata?: Record<string, any>
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    method,
    path,
    ...metadata
  };
  
  console.log(`🚀 ${method} ${path}`, logData);
}

export function logResponse(
  method: string,
  path: string,
  status: number,
  duration?: number
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    method,
    path,
    status,
    ...(duration && { duration_ms: duration })
  };
  
  console.log(`✅ ${method} ${path} - ${status}`, logData);
}

export function logError(
  method: string,
  path: string,
  error: any,
  context?: Record<string, any>
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    method,
    path,
    error: error.message || error,
    stack: error.stack,
    ...context
  };
  
  console.error(`❌ ${method} ${path} - ERROR`, logData);
}
