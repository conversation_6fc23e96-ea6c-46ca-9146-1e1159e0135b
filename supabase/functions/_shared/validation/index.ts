import { z } from 'https://deno.land/x/zod@v3.22.4/mod.ts';

// Base validation schemas extracted from Express API

export const SearchGuidelinesSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  carrier: z.string().optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(50).default(5)
});

export const SearchProceduresSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  limit: z.number().min(1).max(50).default(10)
});

export const ValidateCoverageSchema = z.object({
  carrier: z.string().min(1, 'Carrier is required'),
  procedure_codes: z.array(z.string()).min(1, 'At least one procedure code is required'),
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional()
  }).optional()
});

export const GlossaryLookupSchema = z.object({
  term: z.string().min(1, 'Term is required'),
  exact_match: z.boolean().default(false)
});

// Enhanced schemas for new Edge Function endpoints

export const GenerateNarrativeSchema = z.object({
  claim_data: z.object({
    patient_info: z.object({
      name: z.string().min(1, 'Patient name is required'),
      dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of birth must be in YYYY-MM-DD format'),
      member_id: z.string().optional()
    }),
    procedures: z.array(z.object({
      code: z.string().min(1, 'Procedure code is required'),
      description: z.string().min(1, 'Procedure description is required'),
      date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Procedure date must be in YYYY-MM-DD format'),
      tooth: z.string().optional(),
      surfaces: z.array(z.string()).optional()
    })).min(1, 'At least one procedure is required'),
    diagnosis: z.string().optional(),
    medical_history: z.string().optional(),
    treatment_plan: z.string().optional()
  }),
  carrier: z.string().optional(),
  narrative_type: z.enum(['standard', 'appeal', 'predetermination']).default('standard')
});

export const AnalyzeClaimSchema = z.object({
  claim_data: z.object({
    patient_info: z.object({
      name: z.string().min(1, 'Patient name is required'),
      dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of birth must be in YYYY-MM-DD format'),
      member_id: z.string().optional(),
      age: z.number().min(0).max(150).optional()
    }),
    procedures: z.array(z.object({
      code: z.string().min(1, 'Procedure code is required'),
      description: z.string().optional(),
      date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Procedure date must be in YYYY-MM-DD format'),
      tooth: z.string().optional(),
      surfaces: z.array(z.string()).optional(),
      fee: z.number().positive().optional()
    })).min(1, 'At least one procedure is required'),
    diagnosis: z.string().optional(),
    medical_history: z.string().optional(),
    treatment_plan: z.string().optional(),
    prior_treatments: z.array(z.object({
      code: z.string(),
      date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
      tooth: z.string().optional()
    })).optional()
  }),
  carrier: z.string().min(1, 'Carrier is required'),
  analysis_type: z.enum(['coverage', 'medical_necessity', 'frequency', 'comprehensive']).default('comprehensive')
});

export const DocumentationRequirementsSchema = z.object({
  procedure_codes: z.array(z.string()).min(1, 'At least one procedure code is required'),
  carrier: z.string().min(1, 'Carrier is required'),
  patient_age: z.number().min(0).max(150).optional(),
  diagnosis: z.string().optional(),
  request_type: z.enum(['predetermination', 'claim', 'appeal']).default('claim')
});

export const AppealAssistanceSchema = z.object({
  original_claim: z.object({
    procedures: z.array(z.object({
      code: z.string().min(1),
      description: z.string().optional(),
      date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
      denied_amount: z.number().positive().optional()
    })).min(1),
    denial_reason: z.string().min(1, 'Denial reason is required'),
    denial_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Denial date must be in YYYY-MM-DD format')
  }),
  carrier: z.string().min(1, 'Carrier is required'),
  patient_info: z.object({
    name: z.string().min(1),
    member_id: z.string().optional(),
    age: z.number().min(0).max(150).optional()
  }),
  additional_documentation: z.array(z.string()).optional(),
  appeal_level: z.enum(['first', 'second', 'external']).default('first')
});

export const PredeterminationAnalysisSchema = z.object({
  treatment_plan: z.object({
    procedures: z.array(z.object({
      code: z.string().min(1, 'Procedure code is required'),
      description: z.string().optional(),
      planned_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Planned date must be in YYYY-MM-DD format'),
      tooth: z.string().optional(),
      surfaces: z.array(z.string()).optional(),
      estimated_fee: z.number().positive().optional(),
      priority: z.enum(['urgent', 'high', 'medium', 'low']).default('medium')
    })).min(1, 'At least one procedure is required'),
    diagnosis: z.string().min(1, 'Diagnosis is required'),
    treatment_sequence: z.array(z.string()).optional(),
    alternative_treatments: z.array(z.string()).optional()
  }),
  patient_info: z.object({
    name: z.string().min(1, 'Patient name is required'),
    dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    member_id: z.string().optional(),
    medical_history: z.string().optional(),
    current_oral_health: z.string().optional()
  }),
  carrier: z.string().min(1, 'Carrier is required'),
  analysis_depth: z.enum(['basic', 'comprehensive', 'detailed']).default('comprehensive')
});

// Data endpoint schemas (from data-endpoints.ts)
export const CarrierQuerySchema = z.object({
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

export const ProcedureQuerySchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

export const NetworkQuerySchema = z.object({
  carrier_id: z.number().optional(),
  state: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

export const AppealQuerySchema = z.object({
  carrier: z.string().optional(),
  procedure_type: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

// Utility function to validate request body
export function validateRequestBody<T>(schema: z.ZodSchema<T>, body: unknown): T {
  try {
    return schema.parse(body);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Validation error: ${error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')}`);
    }
    throw error;
  }
}

// Utility function to validate query parameters
export function validateQueryParams<T>(schema: z.ZodSchema<T>, params: Record<string, string>): T {
  try {
    // Convert string values to appropriate types for validation
    const processedParams: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(params)) {
      if (value === undefined || value === null) continue;
      
      // Try to parse numbers
      if (!isNaN(Number(value)) && value.trim() !== '') {
        processedParams[key] = Number(value);
      } 
      // Try to parse booleans
      else if (value.toLowerCase() === 'true' || value.toLowerCase() === 'false') {
        processedParams[key] = value.toLowerCase() === 'true';
      }
      // Keep as string
      else {
        processedParams[key] = value;
      }
    }
    
    return schema.parse(processedParams);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Query parameter validation error: ${error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')}`);
    }
    throw error;
  }
}
