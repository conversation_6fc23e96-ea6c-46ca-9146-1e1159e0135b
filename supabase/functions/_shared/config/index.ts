/**
 * Configuration utilities for Edge Functions
 * Handles environment variables, CORS, and common settings
 */

export interface EdgeFunctionConfig {
  supabase: {
    url: string;
    serviceRoleKey: string;
    anonKey: string;
  };
  openai: {
    apiKey: string;
    model: string;
    embeddingModel: string;
  };
  cors: {
    allowedOrigins: string[];
    allowedMethods: string[];
    allowedHeaders: string[];
  };
  rateLimit: {
    enabled: boolean;
    maxRequests: number;
    windowMs: number;
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    enableRequestLogging: boolean;
  };
}

/**
 * Load and validate configuration from environment variables
 */
export function loadConfig(): EdgeFunctionConfig {
  // Required environment variables
  const supabaseUrl = Deno.env.get('SUPABASE_URL');
  const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

  // Validate required variables
  if (!supabaseUrl) {
    throw new Error('SUPABASE_URL environment variable is required');
  }
  if (!supabaseServiceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  }
  if (!supabaseAnonKey) {
    throw new Error('SUPABASE_ANON_KEY environment variable is required');
  }
  if (!openaiApiKey) {
    throw new Error('OPENAI_API_KEY environment variable is required');
  }

  // Optional environment variables with defaults
  const openaiModel = Deno.env.get('OPENAI_MODEL') || 'gpt-4';
  const openaiEmbeddingModel = Deno.env.get('OPENAI_EMBEDDING_MODEL') || 'text-embedding-3-small';
  
  const allowedOrigins = Deno.env.get('ALLOWED_ORIGINS')?.split(',') || ['*'];
  const allowedMethods = Deno.env.get('ALLOWED_METHODS')?.split(',') || 
    ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
  const allowedHeaders = Deno.env.get('ALLOWED_HEADERS')?.split(',') || 
    ['authorization', 'x-client-info', 'apikey', 'content-type'];

  const rateLimitEnabled = Deno.env.get('RATE_LIMIT_ENABLED') === 'true';
  const rateLimitMaxRequests = parseInt(Deno.env.get('RATE_LIMIT_MAX_REQUESTS') || '100');
  const rateLimitWindowMs = parseInt(Deno.env.get('RATE_LIMIT_WINDOW_MS') || '60000');

  const logLevel = (Deno.env.get('LOG_LEVEL') || 'info') as 'debug' | 'info' | 'warn' | 'error';
  const enableRequestLogging = Deno.env.get('ENABLE_REQUEST_LOGGING') !== 'false';

  return {
    supabase: {
      url: supabaseUrl,
      serviceRoleKey: supabaseServiceRoleKey,
      anonKey: supabaseAnonKey
    },
    openai: {
      apiKey: openaiApiKey,
      model: openaiModel,
      embeddingModel: openaiEmbeddingModel
    },
    cors: {
      allowedOrigins,
      allowedMethods,
      allowedHeaders
    },
    rateLimit: {
      enabled: rateLimitEnabled,
      maxRequests: rateLimitMaxRequests,
      windowMs: rateLimitWindowMs
    },
    logging: {
      level: logLevel,
      enableRequestLogging
    }
  };
}

/**
 * Get CORS headers based on configuration
 */
export function getCorsHeaders(config: EdgeFunctionConfig, origin?: string): Record<string, string> {
  const headers: Record<string, string> = {
    'Access-Control-Allow-Methods': config.cors.allowedMethods.join(', '),
    'Access-Control-Allow-Headers': config.cors.allowedHeaders.join(', '),
    'Access-Control-Max-Age': '86400' // 24 hours
  };

  // Handle origin
  if (config.cors.allowedOrigins.includes('*')) {
    headers['Access-Control-Allow-Origin'] = '*';
  } else if (origin && config.cors.allowedOrigins.includes(origin)) {
    headers['Access-Control-Allow-Origin'] = origin;
    headers['Access-Control-Allow-Credentials'] = 'true';
  }

  return headers;
}

/**
 * Handle CORS preflight requests
 */
export function handleCorsPreflightRequest(request: Request, config: EdgeFunctionConfig): Response {
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(config, origin);

  return new Response(null, {
    status: 200,
    headers: corsHeaders
  });
}

/**
 * Add CORS headers to a response
 */
export function addCorsHeaders(response: Response, config: EdgeFunctionConfig, origin?: string): Response {
  const corsHeaders = getCorsHeaders(config, origin);
  
  // Clone the response to add headers
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: {
      ...Object.fromEntries(response.headers.entries()),
      ...corsHeaders
    }
  });

  return newResponse;
}

/**
 * Validate API key from request headers
 */
export function validateApiKey(request: Request, config: EdgeFunctionConfig): boolean {
  const apiKey = request.headers.get('apikey') || request.headers.get('authorization')?.replace('Bearer ', '');
  
  if (!apiKey) {
    return false;
  }

  // Check against both anon key and service role key
  return apiKey === config.supabase.anonKey || apiKey === config.supabase.serviceRoleKey;
}

/**
 * Extract user information from JWT token
 */
export async function extractUserFromToken(request: Request): Promise<any | null> {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.replace('Bearer ', '');
  
  try {
    // Basic JWT parsing (in production, you'd want proper verification)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = JSON.parse(atob(parts[1]));
    return payload;
  } catch (error) {
    console.error('Failed to parse JWT token:', error);
    return null;
  }
}

/**
 * Rate limiting utilities (simple in-memory implementation)
 */
class SimpleRateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(identifier: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get existing requests for this identifier
    const existingRequests = this.requests.get(identifier) || [];
    
    // Filter out requests outside the window
    const recentRequests = existingRequests.filter(timestamp => timestamp > windowStart);
    
    // Check if under the limit
    if (recentRequests.length >= maxRequests) {
      return false;
    }

    // Add current request and update
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    
    return true;
  }

  cleanup(): void {
    const now = Date.now();
    const maxAge = 60000; // 1 minute
    
    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter(timestamp => timestamp > now - maxAge);
      if (recentRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, recentRequests);
      }
    }
  }
}

const rateLimiter = new SimpleRateLimiter();

// Cleanup rate limiter every 5 minutes
setInterval(() => rateLimiter.cleanup(), 5 * 60 * 1000);

/**
 * Check rate limit for a request
 */
export function checkRateLimit(request: Request, config: EdgeFunctionConfig): boolean {
  if (!config.rateLimit.enabled) {
    return true;
  }

  // Use IP address or user ID as identifier
  const identifier = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

  return rateLimiter.isAllowed(
    identifier,
    config.rateLimit.maxRequests,
    config.rateLimit.windowMs
  );
}

/**
 * Logging utilities based on configuration
 */
export class ConfigurableLogger {
  constructor(private config: EdgeFunctionConfig) {}

  debug(message: string, data?: any): void {
    if (this.shouldLog('debug')) {
      console.debug(`[DEBUG] ${message}`, data);
    }
  }

  info(message: string, data?: any): void {
    if (this.shouldLog('info')) {
      console.info(`[INFO] ${message}`, data);
    }
  }

  warn(message: string, data?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(`[WARN] ${message}`, data);
    }
  }

  error(message: string, data?: any): void {
    if (this.shouldLog('error')) {
      console.error(`[ERROR] ${message}`, data);
    }
  }

  logRequest(request: Request): void {
    if (this.config.logging.enableRequestLogging) {
      const url = new URL(request.url);
      this.info(`${request.method} ${url.pathname}`, {
        userAgent: request.headers.get('user-agent'),
        origin: request.headers.get('origin')
      });
    }
  }

  private shouldLog(level: string): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    const configLevel = levels.indexOf(this.config.logging.level);
    const messageLevel = levels.indexOf(level);
    return messageLevel >= configLevel;
  }
}

// Global configuration instance
let globalConfig: EdgeFunctionConfig | null = null;

/**
 * Get global configuration (loads once and caches)
 */
export function getConfig(): EdgeFunctionConfig {
  if (!globalConfig) {
    globalConfig = loadConfig();
  }
  return globalConfig;
}

/**
 * Get configured logger instance
 */
export function getLogger(): ConfigurableLogger {
  return new ConfigurableLogger(getConfig());
}
