import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import type { 
  VectorSearchResult, 
  GuidelineSearchResult, 
  CarrierInfo, 
  ProcedureInfo, 
  DatabaseStats,
  SearchOptions 
} from '../types/index.ts';

/**
 * Database utilities for Supabase Edge Functions
 * Adapted from the original DatabaseUtils class for serverless environment
 */
export class EdgeDatabaseUtils {
  private static instance: EdgeDatabaseUtils;
  private supabaseUrl: string;
  private supabaseKey: string;
  private openaiApiKey: string;

  private constructor() {
    this.supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    this.supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    this.openaiApiKey = Deno.env.get('OPENAI_API_KEY') || '';
    
    if (!this.supabaseUrl || !this.supabaseKey) {
      throw new Error('Missing required Supabase environment variables');
    }
  }

  public static getInstance(): EdgeDatabaseUtils {
    if (!EdgeDatabaseUtils.instance) {
      EdgeDatabaseUtils.instance = new EdgeDatabaseUtils();
    }
    return EdgeDatabaseUtils.instance;
  }

  /**
   * Create Supabase client instance
   */
  private createClient() {
    return createClient(this.supabaseUrl, this.supabaseKey);
  }

  /**
   * Generate embedding using OpenAI API
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    if (!this.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'text-embedding-3-small',
        input: text,
        encoding_format: 'float',
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data[0].embedding;
  }

  /**
   * Perform vector similarity search on embeddings table
   */
  public async vectorSearch(
    queryText: string,
    options: SearchOptions = {}
  ): Promise<VectorSearchResult[]> {
    const {
      limit = 5,
      similarityThreshold = 0.1,
      carrierFilter,
      categoryFilter
    } = options;

    const supabase = this.createClient();
    
    try {
      // Generate embedding for query
      const queryEmbedding = await this.generateEmbedding(queryText);
      
      // Build the RPC call for vector similarity search
      let rpcCall = supabase.rpc('vector_search', {
        query_embedding: queryEmbedding,
        similarity_threshold: similarityThreshold,
        match_count: limit
      });

      // Apply filters if provided
      if (carrierFilter) {
        rpcCall = rpcCall.filter('carrier_name', 'ilike', `%${carrierFilter}%`);
      }
      
      if (categoryFilter) {
        rpcCall = rpcCall.filter('category', 'ilike', `%${categoryFilter}%`);
      }

      const { data, error } = await rpcCall;
      
      if (error) {
        throw new Error(`Vector search error: ${error.message}`);
      }

      return data || [];
      
    } catch (error) {
      console.error('Vector search failed:', error);
      throw error;
    }
  }

  /**
   * Search guidelines with enhanced metadata
   */
  public async searchGuidelines(
    queryText: string,
    options: SearchOptions = {}
  ): Promise<GuidelineSearchResult[]> {
    const {
      limit = 5,
      similarityThreshold = 0.3,
      carrierFilter,
      categoryFilter
    } = options;

    const supabase = this.createClient();
    
    try {
      // Generate embedding for query
      const queryEmbedding = await this.generateEmbedding(queryText);
      
      // Use RPC function for guideline search with joins
      const { data, error } = await supabase.rpc('search_guidelines_with_metadata', {
        query_embedding: queryEmbedding,
        similarity_threshold: similarityThreshold,
        match_count: limit,
        carrier_filter: carrierFilter || null,
        category_filter: categoryFilter || null
      });
      
      if (error) {
        throw new Error(`Guideline search error: ${error.message}`);
      }

      return data || [];
      
    } catch (error) {
      console.error('Guideline search failed:', error);
      throw error;
    }
  }

  /**
   * Look up carrier information by name
   */
  public async lookupCarrier(carrierName: string): Promise<CarrierInfo | null> {
    const supabase = this.createClient();
    
    try {
      const { data, error } = await supabase
        .from('insurance_carriers')
        .select(`
          id,
          carrier_name,
          carrier_type,
          payer_id,
          claims_address,
          phone_number,
          website,
          contact_info
        `)
        .ilike('carrier_name', `%${carrierName}%`)
        .order('carrier_name')
        .limit(1)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        throw new Error(`Carrier lookup error: ${error.message}`);
      }

      if (!data) {
        return null;
      }

      return {
        id: data.id,
        name: data.carrier_name,
        type: data.carrier_type,
        payer_id: data.payer_id,
        claims_address: data.claims_address,
        phone_number: data.phone_number,
        website: data.website,
        contact_info: data.contact_info
      };
      
    } catch (error) {
      console.error('Carrier lookup failed:', error);
      throw error;
    }
  }

  /**
   * Look up procedure information by CDT codes
   */
  public async lookupProcedures(cdtCodes: string[]): Promise<ProcedureInfo[]> {
    const supabase = this.createClient();
    
    try {
      const { data, error } = await supabase
        .from('procedures')
        .select(`
          id,
          cdt_code,
          name,
          description,
          category
        `)
        .in('cdt_code', cdtCodes);
      
      if (error) {
        throw new Error(`Procedure lookup error: ${error.message}`);
      }

      // Map results and mark which codes were found
      const foundCodes = new Set(data?.map(p => p.cdt_code) || []);
      
      return cdtCodes.map(code => {
        const procedure = data?.find(p => p.cdt_code === code);
        return {
          id: procedure?.id || 0,
          cdt_code: code,
          name: procedure?.name || '',
          description: procedure?.description || '',
          category: procedure?.category || '',
          found: foundCodes.has(code)
        };
      });
      
    } catch (error) {
      console.error('Procedure lookup failed:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  public async getDatabaseStats(): Promise<DatabaseStats> {
    const supabase = this.createClient();
    
    try {
      // Get counts from multiple tables
      const [guidelinesResult, embeddingsResult, carriersResult] = await Promise.all([
        supabase.from('guidelines').select('*', { count: 'exact', head: true }),
        supabase.from('embeddings').select('*', { count: 'exact', head: true }),
        supabase.from('insurance_carriers').select('*', { count: 'exact', head: true })
      ]);

      // Check if vector extension is enabled
      const { data: extensionData } = await supabase.rpc('check_vector_extension');

      return {
        totalGuidelines: guidelinesResult.count || 0,
        totalEmbeddings: embeddingsResult.count || 0,
        totalCarriers: carriersResult.count || 0,
        vectorExtensionEnabled: extensionData || false
      };
      
    } catch (error) {
      console.error('Database stats failed:', error);
      throw error;
    }
  }
}
