import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { EdgeDatabaseUtils } from '../_shared/database/index.ts';
import { AppealAssistanceSchema, validateRequestBody } from '../_shared/validation/index.ts';
import { ResponseBuilder, parseRequestBody, logRequest, logResponse } from '../_shared/responses/index.ts';
import { withErrorHandling } from '../_shared/errors/index.ts';
import { getConfig, handleCorsPreflightRequest, getLogger } from '../_shared/config/index.ts';

const config = getConfig();
const logger = getLogger();
const dbUtils = EdgeDatabaseUtils.getInstance();

/**
 * Provide comprehensive appeal assistance for denied dental claims
 */
async function appealAssistanceHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  logger.logRequest(request);

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return handleCorsPreflightRequest(request, config);
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // Parse and validate request body
  const body = await parseRequestBody(request);
  const validatedData = validateRequestBody(AppealAssistanceSchema, body);
  const { original_claim, carrier, patient_info, additional_documentation, appeal_level } = validatedData;

  logger.info('Providing appeal assistance', {
    carrier,
    appeal_level,
    procedures_count: original_claim.procedures.length,
    denial_reason: original_claim.denial_reason
  });

  // Look up carrier information
  const carrierInfo = await dbUtils.lookupCarrier(carrier);
  if (!carrierInfo) {
    return ResponseBuilder.notFound(`Carrier "${carrier}"`);
  }

  // Analyze the denial reason
  const denialAnalysis = await analyzeDenialReason(
    original_claim.denial_reason,
    original_claim.procedures,
    carrierInfo
  );

  // Generate appeal strategy
  const appealStrategy = await generateAppealStrategy(
    original_claim,
    denialAnalysis,
    carrierInfo,
    appeal_level
  );

  // Create appeal letter template
  const appealLetter = await generateAppealLetter(
    original_claim,
    patient_info,
    denialAnalysis,
    appealStrategy,
    carrierInfo
  );

  // Get required documentation
  const requiredDocumentation = getRequiredAppealDocumentation(
    original_claim.procedures,
    denialAnalysis,
    appeal_level
  );

  // Generate timeline and next steps
  const appealTimeline = getAppealTimeline(appeal_level, carrierInfo);

  const duration = Date.now() - startTime;
  logResponse('POST', '/appeal-assistance', 200, duration);

  return ResponseBuilder.success({
    denial_analysis: denialAnalysis,
    appeal_strategy: appealStrategy,
    appeal_letter_template: appealLetter,
    required_documentation: requiredDocumentation,
    appeal_timeline: appealTimeline,
    carrier_info: {
      name: carrierInfo.name,
      payer_id: carrierInfo.payer_id,
      appeals_contact: carrierInfo.contact_info?.appeals || 'Contact carrier for appeals information'
    },
    success_probability: calculateAppealSuccessProbability(denialAnalysis, appealStrategy)
  }, {
    processing_time_ms: duration,
    appeal_level,
    procedures_analyzed: original_claim.procedures.length
  });
}

/**
 * Analyze the denial reason and find relevant guidelines
 */
async function analyzeDenialReason(
  denialReason: string,
  procedures: any[],
  carrierInfo: any
): Promise<any> {
  const analysis = {
    denial_category: categorizeDenialReason(denialReason),
    specific_issues: [],
    relevant_guidelines: [],
    appeal_grounds: [],
    strength_assessment: 'unknown'
  };

  // Search for guidelines related to the denial reason
  const searchQueries = [
    `${denialReason} appeal guidelines`,
    `${denialReason} coverage requirements`,
    ...procedures.map(proc => `${proc.code} ${denialReason}`)
  ];

  for (const query of searchQueries) {
    try {
      const guidelines = await dbUtils.searchGuidelines(query, {
        limit: 3,
        carrierFilter: carrierInfo.name,
        similarityThreshold: 0.3
      });
      analysis.relevant_guidelines.push(...guidelines);
    } catch (error) {
      logger.warn(`Failed to search guidelines for denial analysis: ${query}`, error);
    }
  }

  // Analyze specific issues based on denial reason
  analysis.specific_issues = identifySpecificIssues(denialReason, procedures);
  
  // Determine appeal grounds
  analysis.appeal_grounds = determineAppealGrounds(
    denialReason,
    analysis.relevant_guidelines,
    procedures
  );

  // Assess strength of appeal
  analysis.strength_assessment = assessAppealStrength(
    analysis.denial_category,
    analysis.relevant_guidelines.length,
    analysis.appeal_grounds.length
  );

  return analysis;
}

/**
 * Categorize the denial reason
 */
function categorizeDenialReason(denialReason: string): string {
  const reason = denialReason.toLowerCase();
  
  if (reason.includes('not covered') || reason.includes('excluded')) {
    return 'coverage_exclusion';
  } else if (reason.includes('frequency') || reason.includes('limitation')) {
    return 'frequency_limitation';
  } else if (reason.includes('medical necessity') || reason.includes('not necessary')) {
    return 'medical_necessity';
  } else if (reason.includes('documentation') || reason.includes('insufficient')) {
    return 'insufficient_documentation';
  } else if (reason.includes('duplicate') || reason.includes('already paid')) {
    return 'duplicate_claim';
  } else if (reason.includes('prior authorization') || reason.includes('predetermination')) {
    return 'prior_authorization';
  } else if (reason.includes('age') || reason.includes('eligibility')) {
    return 'eligibility_issue';
  } else {
    return 'other';
  }
}

/**
 * Identify specific issues to address in appeal
 */
function identifySpecificIssues(denialReason: string, procedures: any[]): string[] {
  const issues = [];
  const reason = denialReason.toLowerCase();

  if (reason.includes('not covered')) {
    issues.push('Challenge coverage determination with policy language');
  }
  
  if (reason.includes('medical necessity')) {
    issues.push('Provide additional clinical justification');
    issues.push('Include supporting literature or guidelines');
  }
  
  if (reason.includes('frequency')) {
    issues.push('Verify frequency calculations');
    issues.push('Provide medical justification for early treatment');
  }
  
  if (reason.includes('documentation')) {
    issues.push('Submit missing or additional documentation');
    issues.push('Clarify clinical findings and treatment rationale');
  }

  if (reason.includes('duplicate')) {
    issues.push('Verify claim is not a duplicate');
    issues.push('Provide explanation of different service dates or procedures');
  }

  return issues;
}

/**
 * Determine grounds for appeal
 */
function determineAppealGrounds(
  denialReason: string,
  guidelines: any[],
  procedures: any[]
): string[] {
  const grounds = [];

  // Check if guidelines support coverage
  const supportiveGuidelines = guidelines.filter(g => 
    g.content.toLowerCase().includes('covered') || 
    g.content.toLowerCase().includes('benefit')
  );

  if (supportiveGuidelines.length > 0) {
    grounds.push('Policy language supports coverage');
  }

  // Standard appeal grounds based on denial category
  const reason = denialReason.toLowerCase();
  
  if (reason.includes('medical necessity')) {
    grounds.push('Treatment is medically necessary based on clinical findings');
    grounds.push('Standard of care supports treatment decision');
  }
  
  if (reason.includes('frequency')) {
    grounds.push('Medical circumstances justify frequency exception');
  }
  
  if (reason.includes('not covered')) {
    grounds.push('Procedure falls under covered benefits');
    grounds.push('Policy interpretation error by carrier');
  }

  return grounds;
}

/**
 * Assess the strength of the appeal
 */
function assessAppealStrength(
  denialCategory: string,
  guidelinesCount: number,
  appealGroundsCount: number
): string {
  let score = 0;

  // Base score by denial category
  switch (denialCategory) {
    case 'insufficient_documentation':
      score += 3; // Usually easiest to appeal
      break;
    case 'medical_necessity':
      score += 2; // Moderate difficulty
      break;
    case 'frequency_limitation':
      score += 2; // Moderate difficulty
      break;
    case 'coverage_exclusion':
      score += 1; // More difficult
      break;
    default:
      score += 1;
  }

  // Add points for supporting evidence
  if (guidelinesCount > 0) score += 1;
  if (guidelinesCount > 2) score += 1;
  if (appealGroundsCount > 2) score += 1;

  if (score >= 5) return 'strong';
  if (score >= 3) return 'moderate';
  return 'weak';
}

/**
 * Generate appeal strategy
 */
async function generateAppealStrategy(
  originalClaim: any,
  denialAnalysis: any,
  carrierInfo: any,
  appealLevel: string
): Promise<any> {
  const strategy = {
    primary_approach: '',
    key_arguments: [],
    supporting_evidence: [],
    documentation_strategy: [],
    timeline_considerations: []
  };

  // Determine primary approach based on denial analysis
  switch (denialAnalysis.denial_category) {
    case 'medical_necessity':
      strategy.primary_approach = 'Demonstrate medical necessity with clinical evidence';
      strategy.key_arguments = [
        'Clinical findings support treatment necessity',
        'Standard of care requires this treatment',
        'Patient health would be compromised without treatment'
      ];
      break;
    
    case 'coverage_exclusion':
      strategy.primary_approach = 'Challenge coverage interpretation';
      strategy.key_arguments = [
        'Procedure falls under covered benefits',
        'Policy language supports coverage',
        'Carrier misinterpreted policy terms'
      ];
      break;
    
    case 'frequency_limitation':
      strategy.primary_approach = 'Justify medical exception to frequency limits';
      strategy.key_arguments = [
        'Medical circumstances warrant early treatment',
        'Patient condition requires more frequent care',
        'Delay would compromise treatment outcome'
      ];
      break;
    
    default:
      strategy.primary_approach = 'Address specific denial reasons with supporting evidence';
      strategy.key_arguments = denialAnalysis.appeal_grounds;
  }

  // Supporting evidence strategy
  strategy.supporting_evidence = [
    'Clinical documentation and notes',
    'Radiographic evidence',
    'Professional literature supporting treatment'
  ];

  if (denialAnalysis.relevant_guidelines.length > 0) {
    strategy.supporting_evidence.push('Carrier\'s own guidelines supporting coverage');
  }

  // Documentation strategy
  strategy.documentation_strategy = [
    'Organize all documentation chronologically',
    'Highlight key clinical findings',
    'Include clear treatment rationale',
    'Reference specific policy language'
  ];

  // Timeline considerations
  if (appealLevel === 'first') {
    strategy.timeline_considerations = [
      'Submit within 180 days of denial',
      'Allow 30-60 days for review',
      'Prepare for potential second-level appeal'
    ];
  } else if (appealLevel === 'second') {
    strategy.timeline_considerations = [
      'Submit within required timeframe from first appeal denial',
      'Include all previous documentation',
      'Consider external review options'
    ];
  }

  return strategy;
}

/**
 * Generate appeal letter template
 */
async function generateAppealLetter(
  originalClaim: any,
  patientInfo: any,
  denialAnalysis: any,
  appealStrategy: any,
  carrierInfo: any
): Promise<string> {
  const procedures = originalClaim.procedures.map(proc => 
    `${proc.code} (${proc.description || 'Procedure'}) - Date: ${proc.date}`
  ).join('\n');

  const letterTemplate = `
[Date]

${carrierInfo.name}
Appeals Department
${carrierInfo.claims_address || '[Claims Address]'}

RE: Appeal for Denied Claim
Patient: ${patientInfo.name}
Member ID: ${patientInfo.member_id || '[Member ID]'}
Claim Date: ${originalClaim.denial_date}
Appeal Level: ${appealStrategy.primary_approach}

Dear Appeals Review Team,

I am writing to formally appeal the denial of the following dental procedures:

${procedures}

DENIAL REASON: ${originalClaim.denial_reason}

GROUNDS FOR APPEAL:
${appealStrategy.key_arguments.map(arg => `• ${arg}`).join('\n')}

CLINICAL JUSTIFICATION:
[Provide detailed clinical justification here, including:]
• Patient's clinical presentation and findings
• Medical necessity for the treatment provided
• Standard of care considerations
• Potential consequences of delayed or denied treatment

SUPPORTING DOCUMENTATION:
${appealStrategy.supporting_evidence.map(evidence => `• ${evidence}`).join('\n')}

POLICY REFERENCE:
[Reference specific policy language that supports coverage]

Based on the clinical evidence and policy language, I respectfully request that you reverse the denial decision and approve payment for the submitted procedures. The treatment provided was medically necessary and falls within the covered benefits under the patient's policy.

I have enclosed all supporting documentation and am available to provide any additional information you may require. Please contact me at [phone number] if you need clarification on any aspect of this appeal.

Thank you for your prompt attention to this matter. I look forward to your favorable response within the required timeframe.

Sincerely,

[Provider Name]
[Provider Credentials]
[Practice Information]

Enclosures:
• Original claim documentation
• Clinical notes and radiographs
• Supporting literature
• Policy references
`;

  return letterTemplate.trim();
}

/**
 * Get required documentation for appeal
 */
function getRequiredAppealDocumentation(
  procedures: any[],
  denialAnalysis: any,
  appealLevel: string
): any {
  const documentation = {
    required: [
      'Original denial letter',
      'Copy of original claim',
      'Appeal letter',
      'Clinical notes and documentation'
    ],
    recommended: [
      'Pre-treatment and post-treatment radiographs',
      'Treatment plan and rationale',
      'Patient medical history',
      'Professional literature supporting treatment'
    ],
    carrier_specific: [],
    appeal_level_specific: []
  };

  // Add procedure-specific documentation
  procedures.forEach(proc => {
    if (proc.code.startsWith('D7')) {
      documentation.recommended.push('Surgical consent forms');
      documentation.recommended.push('Post-operative instructions');
    } else if (proc.code.startsWith('D3')) {
      documentation.recommended.push('Pulp vitality testing results');
      documentation.recommended.push('Working length determination');
    }
  });

  // Add appeal level specific requirements
  if (appealLevel === 'second') {
    documentation.appeal_level_specific = [
      'First appeal denial letter',
      'All previous correspondence',
      'Additional clinical evidence not previously submitted'
    ];
  } else if (appealLevel === 'external') {
    documentation.appeal_level_specific = [
      'All previous appeal documentation',
      'Independent clinical opinion',
      'External review request form'
    ];
  }

  return documentation;
}

/**
 * Get appeal timeline information
 */
function getAppealTimeline(appealLevel: string, carrierInfo: any): any {
  const timeline = {
    submission_deadline: '',
    review_period: '',
    next_steps: [],
    important_dates: []
  };

  switch (appealLevel) {
    case 'first':
      timeline.submission_deadline = '180 days from denial date';
      timeline.review_period = '30-60 business days';
      timeline.next_steps = [
        'Submit complete appeal package',
        'Follow up if no response within review period',
        'Prepare for potential second-level appeal'
      ];
      break;
    
    case 'second':
      timeline.submission_deadline = '60 days from first appeal denial';
      timeline.review_period = '45-90 business days';
      timeline.next_steps = [
        'Submit second-level appeal',
        'Include all new evidence',
        'Consider external review options'
      ];
      break;
    
    case 'external':
      timeline.submission_deadline = '60 days from final internal denial';
      timeline.review_period = '45-60 business days';
      timeline.next_steps = [
        'Submit to external review organization',
        'Provide all documentation',
        'Await independent review decision'
      ];
      break;
  }

  return timeline;
}

/**
 * Calculate appeal success probability
 */
function calculateAppealSuccessProbability(
  denialAnalysis: any,
  appealStrategy: any
): any {
  let probability = 0.3; // Base probability

  // Adjust based on denial category
  switch (denialAnalysis.denial_category) {
    case 'insufficient_documentation':
      probability += 0.4; // High success rate
      break;
    case 'medical_necessity':
      probability += 0.2; // Moderate success rate
      break;
    case 'frequency_limitation':
      probability += 0.2; // Moderate success rate
      break;
    case 'coverage_exclusion':
      probability += 0.1; // Lower success rate
      break;
  }

  // Adjust based on strength assessment
  if (denialAnalysis.strength_assessment === 'strong') {
    probability += 0.2;
  } else if (denialAnalysis.strength_assessment === 'moderate') {
    probability += 0.1;
  }

  // Adjust based on supporting evidence
  if (denialAnalysis.relevant_guidelines.length > 0) {
    probability += 0.1;
  }

  // Cap at 90%
  probability = Math.min(probability, 0.9);

  return {
    percentage: Math.round(probability * 100),
    confidence_level: probability > 0.7 ? 'high' : probability > 0.5 ? 'moderate' : 'low',
    factors: {
      denial_category: denialAnalysis.denial_category,
      strength_assessment: denialAnalysis.strength_assessment,
      supporting_evidence: denialAnalysis.relevant_guidelines.length > 0
    }
  };
}

// Serve the function
serve(withErrorHandling(appealAssistanceHandler, 'appeal-assistance'));
