import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { EdgeDatabaseUtils } from '../_shared/database/index.ts';
import { DocumentationRequirementsSchema, validateRequestBody } from '../_shared/validation/index.ts';
import { ResponseBuilder, parseRequestBody, logRequest, logResponse } from '../_shared/responses/index.ts';
import { withErrorHandling } from '../_shared/errors/index.ts';
import { getConfig, handleCorsPreflightRequest, getLogger } from '../_shared/config/index.ts';

const config = getConfig();
const logger = getLogger();
const dbUtils = EdgeDatabaseUtils.getInstance();

/**
 * Get documentation requirements for specific procedures and carriers
 */
async function documentationRequirementsHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  logger.logRequest(request);

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return handleCorsPreflightRequest(request, config);
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // Parse and validate request body
  const body = await parseRequestBody(request);
  const validatedData = validateRequestBody(DocumentationRequirementsSchema, body);
  const { procedure_codes, carrier, patient_age, diagnosis, request_type } = validatedData;

  logger.info('Getting documentation requirements', {
    carrier,
    request_type,
    procedures_count: procedure_codes.length,
    patient_age
  });

  // Look up carrier information
  const carrierInfo = await dbUtils.lookupCarrier(carrier);
  if (!carrierInfo) {
    return ResponseBuilder.notFound(`Carrier "${carrier}"`);
  }

  // Look up procedure information
  const procedureInfo = await dbUtils.lookupProcedures(procedure_codes);

  // Get documentation requirements for each procedure
  const requirementsByProcedure = await Promise.all(
    procedure_codes.map(code => getDocumentationRequirements(
      code,
      carrierInfo,
      patient_age,
      diagnosis,
      request_type
    ))
  );

  // Compile comprehensive requirements
  const comprehensiveRequirements = compileComprehensiveRequirements(
    requirementsByProcedure,
    procedureInfo,
    carrierInfo,
    request_type
  );

  const duration = Date.now() - startTime;
  logResponse('POST', '/documentation-requirements', 200, duration);

  return ResponseBuilder.success({
    carrier_info: {
      name: carrierInfo.name,
      payer_id: carrierInfo.payer_id
    },
    request_type,
    procedure_requirements: requirementsByProcedure,
    comprehensive_requirements: comprehensiveRequirements,
    compliance_checklist: generateComplianceChecklist(
      comprehensiveRequirements,
      request_type
    ),
    submission_guidelines: getSubmissionGuidelines(carrierInfo, request_type)
  }, {
    processing_time_ms: duration,
    procedures_analyzed: procedure_codes.length,
    requirements_found: requirementsByProcedure.filter(r => r.requirements.length > 0).length
  });
}

/**
 * Get documentation requirements for a specific procedure
 */
async function getDocumentationRequirements(
  procedureCode: string,
  carrierInfo: any,
  patientAge?: number,
  diagnosis?: string,
  requestType: string = 'claim'
): Promise<any> {
  const requirements: any = {
    procedure_code: procedureCode,
    requirements: [],
    carrier_specific: [],
    age_related: [],
    diagnosis_related: [],
    request_type_specific: []
  };

  // Search for procedure-specific guidelines
  const searchQueries = [
    `${procedureCode} documentation requirements`,
    `${procedureCode} ${requestType} requirements`,
    `${procedureCode} ${carrierInfo.name} requirements`
  ];

  if (diagnosis) {
    searchQueries.push(`${procedureCode} ${diagnosis} documentation`);
  }

  for (const query of searchQueries) {
    try {
      const guidelines = await dbUtils.searchGuidelines(query, {
        limit: 3,
        carrierFilter: carrierInfo.name,
        similarityThreshold: 0.3
      });

      // Extract requirements from guidelines
      for (const guideline of guidelines) {
        const extractedReqs = extractRequirementsFromGuideline(
          guideline,
          procedureCode,
          patientAge,
          diagnosis,
          requestType
        );
        
        requirements.requirements.push(...extractedReqs.general);
        requirements.carrier_specific.push(...extractedReqs.carrier_specific);
        requirements.age_related.push(...extractedReqs.age_related);
        requirements.diagnosis_related.push(...extractedReqs.diagnosis_related);
        requirements.request_type_specific.push(...extractedReqs.request_type_specific);
      }
    } catch (error) {
      logger.warn(`Failed to search requirements for ${procedureCode}`, error);
    }
  }

  // Add standard requirements based on procedure category
  const standardReqs = getStandardRequirements(procedureCode, requestType);
  requirements.requirements.push(...standardReqs);

  // Remove duplicates
  requirements.requirements = [...new Set(requirements.requirements)];
  requirements.carrier_specific = [...new Set(requirements.carrier_specific)];
  requirements.age_related = [...new Set(requirements.age_related)];
  requirements.diagnosis_related = [...new Set(requirements.diagnosis_related)];
  requirements.request_type_specific = [...new Set(requirements.request_type_specific)];

  return requirements;
}

/**
 * Extract requirements from guideline content
 */
function extractRequirementsFromGuideline(
  guideline: any,
  procedureCode: string,
  patientAge?: number,
  diagnosis?: string,
  requestType: string = 'claim'
): any {
  const content = guideline.content.toLowerCase();
  const requirements = {
    general: [],
    carrier_specific: [],
    age_related: [],
    diagnosis_related: [],
    request_type_specific: []
  };

  // Common documentation keywords
  const docKeywords = [
    'radiograph', 'x-ray', 'chart', 'note', 'documentation',
    'photograph', 'model', 'impression', 'biopsy', 'pathology',
    'medical history', 'treatment plan', 'consent form'
  ];

  // Extract general requirements
  for (const keyword of docKeywords) {
    if (content.includes(keyword)) {
      if (keyword === 'radiograph' || keyword === 'x-ray') {
        requirements.general.push('Pre-treatment and post-treatment radiographs');
      } else if (keyword === 'photograph') {
        requirements.general.push('Clinical photographs');
      } else if (keyword === 'chart' || keyword === 'note') {
        requirements.general.push('Detailed clinical notes');
      } else if (keyword === 'treatment plan') {
        requirements.general.push('Comprehensive treatment plan');
      } else if (keyword === 'medical history') {
        requirements.general.push('Complete medical and dental history');
      }
    }
  }

  // Extract age-related requirements
  if (content.includes('age') && patientAge) {
    if (content.includes('under 18') || content.includes('pediatric')) {
      requirements.age_related.push('Parental consent required for patients under 18');
    }
    if (content.includes('over 65') || content.includes('senior')) {
      requirements.age_related.push('Additional medical clearance may be required for seniors');
    }
  }

  // Extract diagnosis-related requirements
  if (diagnosis && content.includes(diagnosis.toLowerCase())) {
    requirements.diagnosis_related.push(`Specific documentation required for ${diagnosis}`);
  }

  // Extract request type specific requirements
  if (requestType === 'predetermination' && content.includes('predetermination')) {
    requirements.request_type_specific.push('Treatment plan with estimated fees');
    requirements.request_type_specific.push('Alternative treatment options');
  } else if (requestType === 'appeal' && content.includes('appeal')) {
    requirements.request_type_specific.push('Original denial letter');
    requirements.request_type_specific.push('Additional clinical justification');
  }

  return requirements;
}

/**
 * Get standard requirements based on procedure category
 */
function getStandardRequirements(procedureCode: string, requestType: string): string[] {
  const requirements = [];

  // Basic requirements for all procedures
  requirements.push('Patient identification and insurance information');
  requirements.push('Date of service');
  requirements.push('Provider information and credentials');

  // Procedure category specific requirements
  if (procedureCode.startsWith('D0')) {
    // Diagnostic procedures
    requirements.push('Clinical findings and diagnosis');
  } else if (procedureCode.startsWith('D1')) {
    // Preventive procedures
    requirements.push('Oral hygiene assessment');
    requirements.push('Periodontal charting if applicable');
  } else if (procedureCode.startsWith('D2')) {
    // Restorative procedures
    requirements.push('Pre-treatment radiographs');
    requirements.push('Cavity classification and extent');
    requirements.push('Material selection justification');
  } else if (procedureCode.startsWith('D3')) {
    // Endodontic procedures
    requirements.push('Pulp vitality testing results');
    requirements.push('Pre-treatment and post-treatment radiographs');
    requirements.push('Working length determination');
  } else if (procedureCode.startsWith('D4')) {
    // Periodontal procedures
    requirements.push('Periodontal charting and measurements');
    requirements.push('Radiographic bone level assessment');
    requirements.push('Medical history review');
  } else if (procedureCode.startsWith('D5')) {
    // Prosthodontic procedures
    requirements.push('Pre-treatment photographs');
    requirements.push('Impressions or digital scans');
    requirements.push('Occlusal analysis');
  } else if (procedureCode.startsWith('D6')) {
    // Prosthodontic procedures (fixed)
    requirements.push('Abutment tooth assessment');
    requirements.push('Occlusal analysis');
    requirements.push('Laboratory prescription');
  } else if (procedureCode.startsWith('D7')) {
    // Oral surgery procedures
    requirements.push('Medical history and medications');
    requirements.push('Pre-surgical radiographs');
    requirements.push('Surgical consent form');
    requirements.push('Post-operative instructions');
  }

  // Request type specific requirements
  if (requestType === 'predetermination') {
    requirements.push('Treatment plan with sequence');
    requirements.push('Estimated fees for each procedure');
  } else if (requestType === 'appeal') {
    requirements.push('Copy of original claim');
    requirements.push('Denial explanation');
    requirements.push('Additional supporting documentation');
  }

  return requirements;
}

/**
 * Compile comprehensive requirements from all procedures
 */
function compileComprehensiveRequirements(
  procedureRequirements: any[],
  procedureInfo: any[],
  carrierInfo: any,
  requestType: string
): any {
  const comprehensive = {
    universal_requirements: [],
    procedure_specific: [],
    carrier_requirements: [],
    submission_format: [],
    timeline_requirements: []
  };

  // Collect all unique requirements
  const allRequirements = new Set();
  const carrierSpecific = new Set();
  
  procedureRequirements.forEach(proc => {
    proc.requirements.forEach(req => allRequirements.add(req));
    proc.carrier_specific.forEach(req => carrierSpecific.add(req));
  });

  comprehensive.universal_requirements = Array.from(allRequirements);
  comprehensive.carrier_requirements = Array.from(carrierSpecific);

  // Add submission format requirements
  comprehensive.submission_format = [
    'Submit claims electronically when possible',
    'Include all required attachments',
    'Use current CDT codes',
    'Ensure legible documentation'
  ];

  // Add timeline requirements
  if (requestType === 'predetermination') {
    comprehensive.timeline_requirements = [
      'Submit predetermination before treatment',
      'Allow 10-15 business days for review',
      'Predetermination valid for 6-12 months'
    ];
  } else if (requestType === 'claim') {
    comprehensive.timeline_requirements = [
      'Submit claims within 90 days of service',
      'Include all supporting documentation',
      'Follow up if no response within 30 days'
    ];
  } else if (requestType === 'appeal') {
    comprehensive.timeline_requirements = [
      'Submit appeal within 180 days of denial',
      'Include all required documentation',
      'Allow 30-60 days for appeal review'
    ];
  }

  return comprehensive;
}

/**
 * Generate compliance checklist
 */
function generateComplianceChecklist(requirements: any, requestType: string): string[] {
  const checklist = [];

  // Universal items
  checklist.push('✓ Patient information complete and accurate');
  checklist.push('✓ Provider credentials and information included');
  checklist.push('✓ All required documentation attached');

  // Add specific items based on requirements
  if (requirements.universal_requirements.some(req => req.includes('radiograph'))) {
    checklist.push('✓ Radiographs included and properly labeled');
  }

  if (requirements.universal_requirements.some(req => req.includes('photograph'))) {
    checklist.push('✓ Clinical photographs included');
  }

  // Request type specific
  if (requestType === 'predetermination') {
    checklist.push('✓ Treatment plan with sequence provided');
    checklist.push('✓ Estimated fees included');
  } else if (requestType === 'appeal') {
    checklist.push('✓ Original denial letter included');
    checklist.push('✓ Additional justification provided');
  }

  checklist.push('✓ All documentation legible and complete');
  checklist.push('✓ Submission within required timeframe');

  return checklist;
}

/**
 * Get submission guidelines for carrier and request type
 */
function getSubmissionGuidelines(carrierInfo: any, requestType: string): any {
  return {
    submission_method: 'Electronic submission preferred',
    contact_information: {
      phone: carrierInfo.phone_number || 'Contact carrier for phone number',
      website: carrierInfo.website || 'Contact carrier for website',
      claims_address: carrierInfo.claims_address || 'Contact carrier for claims address'
    },
    processing_time: getProcessingTime(requestType),
    follow_up_timeline: getFollowUpTimeline(requestType)
  };
}

function getProcessingTime(requestType: string): string {
  switch (requestType) {
    case 'predetermination': return '10-15 business days';
    case 'claim': return '15-30 business days';
    case 'appeal': return '30-60 business days';
    default: return '15-30 business days';
  }
}

function getFollowUpTimeline(requestType: string): string {
  switch (requestType) {
    case 'predetermination': return 'Follow up if no response within 20 business days';
    case 'claim': return 'Follow up if no response within 45 business days';
    case 'appeal': return 'Follow up if no response within 75 business days';
    default: return 'Follow up if no response within 45 business days';
  }
}

// Serve the function
serve(withErrorHandling(documentationRequirementsHandler, 'documentation-requirements'));
