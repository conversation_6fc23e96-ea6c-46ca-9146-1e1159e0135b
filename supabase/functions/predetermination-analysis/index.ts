import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { EdgeDatabaseUtils } from '../_shared/database/index.ts';
import { PredeterminationAnalysisSchema, validateRequestBody } from '../_shared/validation/index.ts';
import { ResponseBuilder, parseRequestBody, logRequest, logResponse } from '../_shared/responses/index.ts';
import { withErrorHandling } from '../_shared/errors/index.ts';
import { getConfig, handleCorsPreflightRequest, getLogger } from '../_shared/config/index.ts';

const config = getConfig();
const logger = getLogger();
const dbUtils = EdgeDatabaseUtils.getInstance();

/**
 * Analyze treatment plan for predetermination submission
 */
async function predeterminationAnalysisHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  logger.logRequest(request);

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return handleCorsPreflightRequest(request, config);
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // Parse and validate request body
  const body = await parseRequestBody(request);
  const validatedData = validateRequestBody(PredeterminationAnalysisSchema, body);
  const { treatment_plan, patient_info, carrier, analysis_depth } = validatedData;

  logger.info('Analyzing predetermination', {
    carrier,
    analysis_depth,
    procedures_count: treatment_plan.procedures.length,
    patient_age: calculateAge(patient_info.dob)
  });

  // Look up carrier information
  const carrierInfo = await dbUtils.lookupCarrier(carrier);
  if (!carrierInfo) {
    return ResponseBuilder.notFound(`Carrier "${carrier}"`);
  }

  // Perform comprehensive predetermination analysis
  const analysis = await performPredeterminationAnalysis(
    treatment_plan,
    patient_info,
    carrierInfo,
    analysis_depth
  );

  const duration = Date.now() - startTime;
  logResponse('POST', '/predetermination-analysis', 200, duration);

  return ResponseBuilder.success({
    predetermination_analysis: analysis,
    carrier_info: {
      name: carrierInfo.name,
      payer_id: carrierInfo.payer_id
    },
    submission_recommendations: generateSubmissionRecommendations(analysis, carrierInfo),
    estimated_timeline: getEstimatedTimeline(analysis, carrierInfo)
  }, {
    processing_time_ms: duration,
    analysis_depth,
    procedures_analyzed: treatment_plan.procedures.length
  });
}

/**
 * Calculate age from date of birth
 */
function calculateAge(dob: string): number {
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

/**
 * Perform comprehensive predetermination analysis
 */
async function performPredeterminationAnalysis(
  treatmentPlan: any,
  patientInfo: any,
  carrierInfo: any,
  analysisDepth: string
): Promise<any> {
  const analysis: any = {
    overall_assessment: {},
    procedure_analysis: [],
    coverage_predictions: {},
    risk_assessment: {},
    optimization_recommendations: [],
    required_documentation: [],
    alternative_treatments: []
  };

  const patientAge = calculateAge(patientInfo.dob);

  // Analyze each procedure
  for (const procedure of treatmentPlan.procedures) {
    const procAnalysis = await analyzeProcedureForPredetermination(
      procedure,
      treatmentPlan,
      patientInfo,
      patientAge,
      carrierInfo,
      analysisDepth
    );
    analysis.procedure_analysis.push(procAnalysis);
  }

  // Perform treatment sequence analysis
  if (analysisDepth === 'comprehensive' || analysisDepth === 'detailed') {
    analysis.sequence_analysis = analyzeTreatmentSequence(
      treatmentPlan.procedures,
      treatmentPlan.treatment_sequence
    );
  }

  // Generate coverage predictions
  analysis.coverage_predictions = generateCoveragePredictions(
    analysis.procedure_analysis,
    carrierInfo,
    patientAge
  );

  // Assess overall risk
  analysis.risk_assessment = assessPredeterminationRisk(
    analysis.procedure_analysis,
    treatmentPlan,
    patientInfo
  );

  // Generate optimization recommendations
  analysis.optimization_recommendations = generateOptimizationRecommendations(
    analysis.procedure_analysis,
    analysis.coverage_predictions,
    treatmentPlan
  );

  // Compile required documentation
  analysis.required_documentation = compileRequiredDocumentation(
    treatmentPlan.procedures,
    analysis.procedure_analysis,
    carrierInfo
  );

  // Suggest alternative treatments if applicable
  if (analysisDepth === 'detailed') {
    analysis.alternative_treatments = await suggestAlternativeTreatments(
      treatmentPlan.procedures,
      analysis.procedure_analysis,
      carrierInfo
    );
  }

  // Overall assessment
  analysis.overall_assessment = {
    total_procedures: treatmentPlan.procedures.length,
    estimated_approval_rate: calculateEstimatedApprovalRate(analysis.procedure_analysis),
    total_estimated_cost: calculateTotalEstimatedCost(treatmentPlan.procedures),
    high_risk_procedures: analysis.procedure_analysis.filter(p => p.risk_level === 'high').length,
    approval_likelihood: determineOverallApprovalLikelihood(analysis.procedure_analysis)
  };

  return analysis;
}

/**
 * Analyze individual procedure for predetermination
 */
async function analyzeProcedureForPredetermination(
  procedure: any,
  treatmentPlan: any,
  patientInfo: any,
  patientAge: number,
  carrierInfo: any,
  analysisDepth: string
): Promise<any> {
  const analysis: any = {
    procedure_code: procedure.code,
    procedure_description: procedure.description,
    estimated_fee: procedure.estimated_fee || 0,
    coverage_likelihood: 'unknown',
    risk_level: 'low',
    risk_factors: [],
    coverage_percentage: 0,
    patient_responsibility: 0,
    requirements: [],
    recommendations: []
  };

  // Search for coverage guidelines
  try {
    const guidelines = await dbUtils.searchGuidelines(
      `${procedure.code} coverage predetermination`,
      {
        limit: 3,
        carrierFilter: carrierInfo.name,
        similarityThreshold: 0.3
      }
    );

    if (guidelines.length > 0) {
      analysis.coverage_likelihood = 'covered';
      analysis.coverage_percentage = estimateCoveragePercentage(procedure.code, guidelines);
      
      // Extract requirements from guidelines
      analysis.requirements = extractRequirementsFromGuidelines(guidelines, procedure);
    } else {
      analysis.coverage_likelihood = 'questionable';
      analysis.risk_level = 'medium';
      analysis.risk_factors.push('No specific coverage guidelines found');
    }
  } catch (error) {
    logger.warn(`Failed to search guidelines for ${procedure.code}`, error);
    analysis.coverage_likelihood = 'unknown';
    analysis.risk_factors.push('Unable to verify coverage guidelines');
  }

  // Analyze age-related factors
  if (patientAge < 18 && procedure.code.startsWith('D1')) {
    analysis.coverage_percentage = Math.max(analysis.coverage_percentage, 80); // Preventive care for children
  } else if (patientAge > 65 && procedure.code.startsWith('D5')) {
    analysis.risk_factors.push('Prosthodontic coverage may be limited for seniors');
  }

  // Calculate patient responsibility
  analysis.patient_responsibility = analysis.estimated_fee * (1 - analysis.coverage_percentage / 100);

  // Generate procedure-specific recommendations
  analysis.recommendations = generateProcedureRecommendations(
    procedure,
    analysis,
    treatmentPlan.diagnosis
  );

  return analysis;
}

/**
 * Estimate coverage percentage based on procedure and guidelines
 */
function estimateCoveragePercentage(procedureCode: string, guidelines: any[]): number {
  // Default coverage percentages by procedure category
  let basePercentage = 50; // Default

  if (procedureCode.startsWith('D0') || procedureCode.startsWith('D1')) {
    basePercentage = 80; // Diagnostic and preventive
  } else if (procedureCode.startsWith('D2')) {
    basePercentage = 70; // Restorative
  } else if (procedureCode.startsWith('D3')) {
    basePercentage = 60; // Endodontic
  } else if (procedureCode.startsWith('D4')) {
    basePercentage = 60; // Periodontal
  } else if (procedureCode.startsWith('D5') || procedureCode.startsWith('D6')) {
    basePercentage = 50; // Prosthodontic
  } else if (procedureCode.startsWith('D7')) {
    basePercentage = 70; // Oral surgery
  }

  // Adjust based on guideline content
  const coverageContent = guidelines.find(g => 
    g.content.toLowerCase().includes('percent') || 
    g.content.toLowerCase().includes('%')
  );

  if (coverageContent) {
    const percentMatch = coverageContent.content.match(/(\d+)%/);
    if (percentMatch) {
      return parseInt(percentMatch[1]);
    }
  }

  return basePercentage;
}

/**
 * Extract requirements from guidelines
 */
function extractRequirementsFromGuidelines(guidelines: any[], procedure: any): string[] {
  const requirements = [];
  
  for (const guideline of guidelines) {
    const content = guideline.content.toLowerCase();
    
    if (content.includes('radiograph') || content.includes('x-ray')) {
      requirements.push('Radiographic documentation required');
    }
    if (content.includes('photograph')) {
      requirements.push('Clinical photographs required');
    }
    if (content.includes('model') || content.includes('impression')) {
      requirements.push('Study models or impressions required');
    }
    if (content.includes('narrative') || content.includes('explanation')) {
      requirements.push('Detailed treatment narrative required');
    }
  }
  
  return [...new Set(requirements)]; // Remove duplicates
}

/**
 * Generate procedure-specific recommendations
 */
function generateProcedureRecommendations(
  procedure: any,
  analysis: any,
  diagnosis?: string
): string[] {
  const recommendations = [];

  if (analysis.coverage_likelihood === 'questionable') {
    recommendations.push('Consider alternative treatment options');
    recommendations.push('Verify coverage with carrier before treatment');
  }

  if (analysis.risk_factors.length > 0) {
    recommendations.push('Address identified risk factors before submission');
  }

  if (procedure.priority === 'urgent') {
    recommendations.push('Emphasize urgency in predetermination request');
  }

  // Procedure-specific recommendations
  if (procedure.code.startsWith('D6') || procedure.code.startsWith('D5')) {
    recommendations.push('Include detailed prosthodontic treatment plan');
    recommendations.push('Consider phased treatment approach');
  }

  return recommendations;
}

/**
 * Analyze treatment sequence
 */
function analyzeTreatmentSequence(
  procedures: any[],
  treatmentSequence?: string[]
): any {
  return {
    sequence_appropriateness: 'appropriate',
    recommendations: [
      'Follow logical treatment sequence',
      'Complete diagnostic procedures first',
      'Address urgent needs before elective treatment'
    ],
    optimization_opportunities: []
  };
}

/**
 * Generate coverage predictions
 */
function generateCoveragePredictions(
  procedureAnalysis: any[],
  carrierInfo: any,
  patientAge: number
): any {
  const totalEstimated = procedureAnalysis.reduce((sum, proc) => sum + (proc.estimated_fee || 0), 0);
  const totalCovered = procedureAnalysis.reduce((sum, proc) =>
    sum + ((proc.estimated_fee || 0) * (proc.coverage_percentage / 100)), 0
  );

  return {
    total_estimated_cost: totalEstimated,
    total_estimated_coverage: totalCovered,
    patient_responsibility: totalEstimated - totalCovered,
    coverage_percentage: totalEstimated > 0 ? (totalCovered / totalEstimated) * 100 : 0
  };
}

/**
 * Assess predetermination risk
 */
function assessPredeterminationRisk(
  procedureAnalysis: any[],
  treatmentPlan: any,
  patientInfo: any
): any {
  const highRiskCount = procedureAnalysis.filter(p => p.risk_level === 'high').length;
  const totalProcedures = procedureAnalysis.length;

  return {
    overall_risk_level: highRiskCount > totalProcedures * 0.3 ? 'high' : 'low',
    risk_factors: procedureAnalysis.flatMap(p => p.risk_factors),
    mitigation_strategies: [
      'Provide comprehensive documentation',
      'Include detailed treatment rationale',
      'Consider phased treatment approach'
    ]
  };
}

/**
 * Generate optimization recommendations
 */
function generateOptimizationRecommendations(
  procedureAnalysis: any[],
  coveragePredictions: any,
  treatmentPlan: any
): string[] {
  const recommendations = [];

  if (coveragePredictions.coverage_percentage < 60) {
    recommendations.push('Consider alternative treatments with better coverage');
  }

  const highRiskProcedures = procedureAnalysis.filter(p => p.risk_level === 'high');
  if (highRiskProcedures.length > 0) {
    recommendations.push('Address high-risk procedures with additional documentation');
  }

  recommendations.push('Submit predetermination before beginning treatment');
  recommendations.push('Maintain detailed clinical documentation');

  return recommendations;
}

/**
 * Compile required documentation
 */
function compileRequiredDocumentation(
  procedures: any[],
  procedureAnalysis: any[],
  carrierInfo: any
): string[] {
  const documentation = new Set([
    'Comprehensive treatment plan',
    'Clinical examination findings',
    'Radiographic documentation',
    'Patient medical and dental history'
  ]);

  procedureAnalysis.forEach(proc => {
    proc.requirements.forEach(req => documentation.add(req));
  });

  return Array.from(documentation);
}

/**
 * Suggest alternative treatments
 */
async function suggestAlternativeTreatments(
  procedures: any[],
  procedureAnalysis: any[],
  carrierInfo: any
): Promise<any[]> {
  // Simplified alternative treatment suggestions
  return procedures.map(proc => ({
    original_procedure: proc.code,
    alternatives: getAlternativeProcedures(proc.code),
    rationale: 'Consider alternatives for better coverage or lower cost'
  }));
}

/**
 * Get alternative procedures
 */
function getAlternativeProcedures(procedureCode: string): string[] {
  const alternatives = [];

  // Simplified alternative mapping
  if (procedureCode === 'D2750') { // Crown
    alternatives.push('D2391'); // Large filling
  } else if (procedureCode === 'D6240') { // Pontic
    alternatives.push('D5213'); // Partial denture
  }

  return alternatives;
}

/**
 * Calculate estimated approval rate
 */
function calculateEstimatedApprovalRate(procedureAnalysis: any[]): number {
  const coveredProcedures = procedureAnalysis.filter(p => p.coverage_likelihood === 'covered').length;
  return procedureAnalysis.length > 0 ? (coveredProcedures / procedureAnalysis.length) * 100 : 0;
}

/**
 * Calculate total estimated cost
 */
function calculateTotalEstimatedCost(procedures: any[]): number {
  return procedures.reduce((sum, proc) => sum + (proc.estimated_fee || 0), 0);
}

/**
 * Determine overall approval likelihood
 */
function determineOverallApprovalLikelihood(procedureAnalysis: any[]): string {
  const approvalRate = calculateEstimatedApprovalRate(procedureAnalysis);

  if (approvalRate >= 80) return 'high';
  if (approvalRate >= 60) return 'medium';
  return 'low';
}

/**
 * Generate submission recommendations
 */
function generateSubmissionRecommendations(analysis: any, carrierInfo: any): string[] {
  return [
    'Submit complete predetermination package',
    'Include all required documentation',
    'Follow carrier-specific submission guidelines',
    'Allow adequate time for review before treatment'
  ];
}

/**
 * Get estimated timeline
 */
function getEstimatedTimeline(analysis: any, carrierInfo: any): any {
  return {
    submission_to_review: '1-2 business days',
    review_period: '10-15 business days',
    total_timeline: '2-3 weeks',
    validity_period: '6-12 months'
  };
}

// Serve the function
serve(withErrorHandling(predeterminationAnalysisHandler, 'predetermination-analysis'));
