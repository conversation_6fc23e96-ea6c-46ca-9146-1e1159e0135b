import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

/**
 * Simple test function to verify deployment works
 */
async function testSearchHandler(request: Request): Promise<Response> {
  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    );
  }

  try {
    // Parse request body
    const body = await request.json();
    const { query, limit = 5 } = body;

    if (!query) {
      return new Response(
        JSON.stringify({ error: 'Query is required' }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Mock response for testing
    const mockResults = [
      {
        id: 1,
        title: `Mock guideline for: ${query}`,
        category: 'Restorative',
        carrier: 'Test Insurance',
        similarity_score: 0.95,
        content: `This is a mock guideline result for the query "${query}". In a real implementation, this would be retrieved from the database using vector similarity search.`,
        relevance_score: 0.95,
        content_preview: `This is a mock guideline result for the query "${query}". In a real implementation...`,
        match_type: 'high',
        keywords: query.toLowerCase().split(' ')
      }
    ];

    const response = {
      success: true,
      data: {
        results: mockResults.slice(0, limit),
        total_found: mockResults.length,
        search_metadata: {
          query,
          similarity_threshold: 0.2,
          processing_time_ms: 50,
          search_quality: 'high',
          has_carrier_specific_results: true,
          top_categories: ['Restorative']
        }
      }
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    );

  } catch (error) {
    console.error('Error in test-search function:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    );
  }
}

// Serve the function
serve(testSearchHandler);
