/**
 * Comprehensive test suite for all Edge Functions
 * Run with: deno run --allow-net --allow-env comprehensive-test-suite.ts
 */

import {
  testSearchGuidelinesRequest,
  testGenerateNarrativeRequest,
  testAnalyzeClaimRequest,
  mockGuidelinesSearchResult,
  mockCarrierInfo,
  mockProcedureInfo
} from './test-data.ts';

const BASE_URL = Deno.env.get('EDGE_FUNCTIONS_URL') || 'http://localhost:54321/functions/v1';
const API_KEY = Deno.env.get('SUPABASE_ANON_KEY') || 'test-anon-key';

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
  response?: any;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: number;
  failed: number;
  totalDuration: number;
}

class ComprehensiveTestRunner {
  private testSuites: TestSuite[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Comprehensive Edge Function Test Suite\n');
    console.log('='.repeat(60));

    // Core Functions Tests
    await this.runTestSuite('Core Functions', [
      () => this.testSearchGuidelines(),
      () => this.testGenerateNarrative(),
      () => this.testAnalyzeClaim()
    ]);

    // Enhanced Functions Tests
    await this.runTestSuite('Enhanced Functions', [
      () => this.testDocumentationRequirements(),
      () => this.testAppealAssistance(),
      () => this.testPredeterminationAnalysis()
    ]);

    // Security and Error Handling Tests
    await this.runTestSuite('Security & Error Handling', [
      () => this.testRateLimiting(),
      () => this.testAuthentication(),
      () => this.testInputValidation(),
      () => this.testErrorHandling()
    ]);

    // Performance Tests
    await this.runTestSuite('Performance Tests', [
      () => this.testResponseTimes(),
      () => this.testConcurrentRequests(),
      () => this.testLargePayloads()
    ]);

    // Integration Tests
    await this.runTestSuite('Integration Tests', [
      () => this.testDatabaseConnectivity(),
      () => this.testOpenAIIntegration(),
      () => this.testCORSHandling()
    ]);

    this.printFinalResults();
  }

  private async runTestSuite(suiteName: string, tests: (() => Promise<TestResult>)[]): Promise<void> {
    console.log(`\n📋 Running ${suiteName} Test Suite`);
    console.log('-'.repeat(40));

    const suite: TestSuite = {
      name: suiteName,
      tests: [],
      passed: 0,
      failed: 0,
      totalDuration: 0
    };

    for (const test of tests) {
      try {
        const result = await test();
        suite.tests.push(result);
        suite.totalDuration += result.duration;
        
        if (result.passed) {
          suite.passed++;
          console.log(`✅ ${result.name} (${result.duration}ms)`);
        } else {
          suite.failed++;
          console.log(`❌ ${result.name} (${result.duration}ms) - ${result.error}`);
        }
      } catch (error) {
        const failedResult: TestResult = {
          name: 'Unknown Test',
          passed: false,
          error: error.message,
          duration: 0
        };
        suite.tests.push(failedResult);
        suite.failed++;
        console.log(`❌ Test failed: ${error.message}`);
      }
    }

    this.testSuites.push(suite);
    console.log(`\n📊 ${suiteName} Results: ${suite.passed}/${suite.tests.length} passed (${Math.round(suite.totalDuration)}ms total)`);
  }

  // Core Function Tests
  private async testSearchGuidelines(): Promise<TestResult> {
    return this.makeRequest(
      'Search Guidelines - Basic',
      'POST',
      '/search-guidelines',
      testSearchGuidelinesRequest,
      (response) => this.validateSearchGuidelinesResponse(response)
    );
  }

  private async testGenerateNarrative(): Promise<TestResult> {
    return this.makeRequest(
      'Generate Narrative - Standard',
      'POST',
      '/generate-narrative',
      testGenerateNarrativeRequest,
      (response) => this.validateGenerateNarrativeResponse(response)
    );
  }

  private async testAnalyzeClaim(): Promise<TestResult> {
    return this.makeRequest(
      'Analyze Claim - Comprehensive',
      'POST',
      '/analyze-claim',
      testAnalyzeClaimRequest,
      (response) => this.validateAnalyzeClaimResponse(response)
    );
  }

  // Enhanced Function Tests
  private async testDocumentationRequirements(): Promise<TestResult> {
    const payload = {
      procedure_codes: ['D1110', 'D0150'],
      carrier: 'Delta Dental',
      request_type: 'claim'
    };

    return this.makeRequest(
      'Documentation Requirements',
      'POST',
      '/documentation-requirements',
      payload,
      (response) => response.success && response.data?.comprehensive_requirements
    );
  }

  private async testAppealAssistance(): Promise<TestResult> {
    const payload = {
      original_claim: {
        procedures: [{ code: 'D2750', description: 'Crown', date: '2024-12-20' }],
        denial_reason: 'Not medically necessary',
        denial_date: '2024-12-25'
      },
      carrier: 'Aetna',
      patient_info: { name: 'Test Patient' },
      appeal_level: 'first'
    };

    return this.makeRequest(
      'Appeal Assistance',
      'POST',
      '/appeal-assistance',
      payload,
      (response) => response.success && response.data?.appeal_strategy
    );
  }

  private async testPredeterminationAnalysis(): Promise<TestResult> {
    const payload = {
      treatment_plan: {
        procedures: [{ 
          code: 'D2750', 
          description: 'Crown',
          planned_date: '2024-12-30',
          estimated_fee: 1200
        }],
        diagnosis: 'Extensive caries'
      },
      patient_info: { name: 'Test Patient', dob: '1990-01-01' },
      carrier: 'Cigna',
      analysis_depth: 'comprehensive'
    };

    return this.makeRequest(
      'Predetermination Analysis',
      'POST',
      '/predetermination-analysis',
      payload,
      (response) => response.success && response.data?.predetermination_analysis
    );
  }

  // Security Tests
  private async testRateLimiting(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      // Make multiple rapid requests to test rate limiting
      const promises = Array(10).fill(null).map(() => 
        fetch(`${BASE_URL}/search-guidelines`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'apikey': API_KEY },
          body: JSON.stringify({ query: 'test', limit: 1 })
        })
      );

      const responses = await Promise.all(promises);
      const rateLimited = responses.some(r => r.status === 429);

      return {
        name: 'Rate Limiting',
        passed: true, // Rate limiting is optional, so we pass if no errors
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        name: 'Rate Limiting',
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  private async testAuthentication(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${BASE_URL}/search-guidelines`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }, // No API key
        body: JSON.stringify({ query: 'test' })
      });

      const isUnauthorized = response.status === 401;

      return {
        name: 'Authentication Required',
        passed: isUnauthorized,
        error: !isUnauthorized ? 'Should require authentication' : undefined,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        name: 'Authentication Required',
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  private async testInputValidation(): Promise<TestResult> {
    return this.makeRequest(
      'Input Validation',
      'POST',
      '/search-guidelines',
      { invalid: 'payload' }, // Invalid payload
      (response) => !response.success && response.error === 'VALIDATION_ERROR'
    );
  }

  private async testErrorHandling(): Promise<TestResult> {
    return this.makeRequest(
      'Error Handling',
      'POST',
      '/search-guidelines',
      'invalid json', // Invalid JSON
      (response) => !response.success,
      false // Don't parse as JSON
    );
  }

  // Performance Tests
  private async testResponseTimes(): Promise<TestResult> {
    const startTime = Date.now();
    
    const result = await this.makeRequest(
      'Response Time Check',
      'POST',
      '/search-guidelines',
      { query: 'D1110', limit: 3 },
      (response) => response.success
    );

    // Check if response time is reasonable (< 5 seconds)
    const isReasonable = result.duration < 5000;
    
    return {
      ...result,
      passed: result.passed && isReasonable,
      error: !isReasonable ? 'Response time too slow' : result.error
    };
  }

  private async testConcurrentRequests(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const promises = Array(5).fill(null).map(() => 
        this.makeRequest(
          'Concurrent Test',
          'POST',
          '/search-guidelines',
          { query: 'test', limit: 1 },
          (response) => response.success
        )
      );

      const results = await Promise.all(promises);
      const allPassed = results.every(r => r.passed);

      return {
        name: 'Concurrent Requests',
        passed: allPassed,
        error: !allPassed ? 'Some concurrent requests failed' : undefined,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        name: 'Concurrent Requests',
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  private async testLargePayloads(): Promise<TestResult> {
    const largePayload = {
      claim_data: {
        patient_info: { name: 'Test Patient', dob: '1990-01-01' },
        procedures: Array(20).fill(null).map((_, i) => ({
          code: 'D1110',
          description: 'Test procedure '.repeat(50), // Large description
          date: '2024-12-20'
        })),
        diagnosis: 'Test diagnosis '.repeat(100) // Large diagnosis
      },
      narrative_type: 'standard'
    };

    return this.makeRequest(
      'Large Payload Handling',
      'POST',
      '/generate-narrative',
      largePayload,
      (response) => response.success || response.error === 'VALIDATION_ERROR'
    );
  }

  // Integration Tests
  private async testDatabaseConnectivity(): Promise<TestResult> {
    return this.makeRequest(
      'Database Connectivity',
      'POST',
      '/search-guidelines',
      { query: 'database test', limit: 1 },
      (response) => response.success || response.error !== 'DATABASE_CONNECTION_ERROR'
    );
  }

  private async testOpenAIIntegration(): Promise<TestResult> {
    return this.makeRequest(
      'OpenAI Integration',
      'POST',
      '/generate-narrative',
      {
        claim_data: {
          patient_info: { name: 'Test Patient', dob: '1990-01-01' },
          procedures: [{ code: 'D1110', description: 'Test', date: '2024-12-20' }]
        }
      },
      (response) => response.success && response.data?.narrative
    );
  }

  private async testCORSHandling(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${BASE_URL}/search-guidelines`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://example.com',
          'Access-Control-Request-Method': 'POST'
        }
      });

      const hasCorsHeaders = response.headers.get('Access-Control-Allow-Origin') !== null;

      return {
        name: 'CORS Handling',
        passed: response.ok && hasCorsHeaders,
        error: !hasCorsHeaders ? 'Missing CORS headers' : undefined,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        name: 'CORS Handling',
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  // Helper Methods
  private async makeRequest(
    testName: string,
    method: string,
    endpoint: string,
    payload: any,
    validator: (response: any) => boolean,
    parseJson: boolean = true
  ): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'apikey': API_KEY
        },
        body: typeof payload === 'string' ? payload : JSON.stringify(payload)
      });

      const duration = Date.now() - startTime;
      
      if (!parseJson) {
        return {
          name: testName,
          passed: validator({ success: response.ok }),
          duration
        };
      }

      const responseData = await response.json();
      const isValid = validator(responseData);

      return {
        name: testName,
        passed: isValid,
        duration,
        response: responseData,
        error: !isValid ? 'Validation failed' : undefined
      };
    } catch (error) {
      return {
        name: testName,
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  // Response Validators
  private validateSearchGuidelinesResponse(response: any): boolean {
    return response.success && 
           response.data && 
           Array.isArray(response.data.results) &&
           typeof response.data.total_found === 'number';
  }

  private validateGenerateNarrativeResponse(response: any): boolean {
    return response.success && 
           response.data && 
           typeof response.data.narrative === 'string' &&
           Array.isArray(response.data.recommendations);
  }

  private validateAnalyzeClaimResponse(response: any): boolean {
    return response.success && 
           response.data && 
           response.data.claim_analysis &&
           response.data.carrier_info;
  }

  private printFinalResults(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 FINAL TEST RESULTS');
    console.log('='.repeat(60));

    let totalTests = 0;
    let totalPassed = 0;
    let totalDuration = 0;

    this.testSuites.forEach(suite => {
      totalTests += suite.tests.length;
      totalPassed += suite.passed;
      totalDuration += suite.totalDuration;

      const passRate = Math.round((suite.passed / suite.tests.length) * 100);
      console.log(`${suite.name}: ${suite.passed}/${suite.tests.length} (${passRate}%)`);
    });

    const overallPassRate = Math.round((totalPassed / totalTests) * 100);
    console.log('\n' + '-'.repeat(40));
    console.log(`OVERALL: ${totalPassed}/${totalTests} tests passed (${overallPassRate}%)`);
    console.log(`Total Duration: ${Math.round(totalDuration)}ms`);
    console.log(`Average Test Time: ${Math.round(totalDuration / totalTests)}ms`);

    if (overallPassRate >= 90) {
      console.log('\n🎉 EXCELLENT! Test suite passed with flying colors!');
    } else if (overallPassRate >= 75) {
      console.log('\n✅ GOOD! Most tests passed. Review failed tests.');
    } else {
      console.log('\n⚠️  NEEDS WORK! Many tests failed. Review implementation.');
    }

    // Show failed tests
    const failedTests = this.testSuites.flatMap(suite => 
      suite.tests.filter(test => !test.passed)
    );

    if (failedTests.length > 0) {
      console.log('\n❌ Failed Tests:');
      failedTests.forEach(test => {
        console.log(`  - ${test.name}: ${test.error}`);
      });
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.main) {
  const runner = new ComprehensiveTestRunner();
  await runner.runAllTests();
}
