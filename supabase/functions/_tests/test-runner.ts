/**
 * Test runner for Edge Functions
 * Run with: deno run --allow-net --allow-env test-runner.ts
 */

import {
  testSearchGuidelinesRequest,
  testGenerateNarrativeRequest,
  testAnalyzeClaimRequest,
  expectedSearchGuidelinesResponse,
  expectedGenerateNarrativeResponse,
  expectedAnalyzeClaimResponse
} from './test-data.ts';

const BASE_URL = 'http://localhost:54321/functions/v1';

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
  response?: any;
}

class EdgeFunctionTester {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Edge Function Tests\n');

    // Test search-guidelines
    await this.testFunction(
      'search-guidelines',
      'POST',
      testSearchGuidelinesRequest,
      this.validateSearchGuidelinesResponse.bind(this)
    );

    // Test generate-narrative
    await this.testFunction(
      'generate-narrative',
      'POST',
      testGenerateNarrativeRequest,
      this.validateGenerateNarrativeResponse.bind(this)
    );

    // Test analyze-claim
    await this.testFunction(
      'analyze-claim',
      'POST',
      testAnalyzeClaimRequest,
      this.validateAnalyzeClaimResponse.bind(this)
    );

    // Test CORS preflight
    await this.testCors('search-guidelines');
    await this.testCors('generate-narrative');
    await this.testCors('analyze-claim');

    // Test error handling
    await this.testErrorHandling();

    this.printResults();
  }

  private async testFunction(
    functionName: string,
    method: string,
    payload: any,
    validator: (response: any) => boolean
  ): Promise<void> {
    const testName = `${functionName} - ${method}`;
    const startTime = Date.now();

    try {
      const response = await fetch(`${BASE_URL}/${functionName}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'apikey': 'test-anon-key'
        },
        body: JSON.stringify(payload)
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${responseData.message || 'Unknown error'}`);
      }

      const isValid = validator(responseData);
      
      this.results.push({
        name: testName,
        passed: isValid,
        duration,
        response: responseData,
        error: isValid ? undefined : 'Response validation failed'
      });

      console.log(`${isValid ? '✅' : '❌'} ${testName} (${duration}ms)`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name: testName,
        passed: false,
        duration,
        error: error.message
      });

      console.log(`❌ ${testName} (${duration}ms) - ${error.message}`);
    }
  }

  private async testCors(functionName: string): Promise<void> {
    const testName = `${functionName} - CORS`;
    const startTime = Date.now();

    try {
      const response = await fetch(`${BASE_URL}/${functionName}`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://example.com',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'content-type'
        }
      });

      const duration = Date.now() - startTime;
      
      const hasCorrectHeaders = 
        response.headers.get('Access-Control-Allow-Origin') === '*' &&
        response.headers.get('Access-Control-Allow-Methods')?.includes('POST') &&
        response.headers.get('Access-Control-Allow-Headers')?.includes('content-type');

      this.results.push({
        name: testName,
        passed: response.ok && hasCorrectHeaders,
        duration,
        error: !hasCorrectHeaders ? 'Missing or incorrect CORS headers' : undefined
      });

      console.log(`${response.ok && hasCorrectHeaders ? '✅' : '❌'} ${testName} (${duration}ms)`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name: testName,
        passed: false,
        duration,
        error: error.message
      });

      console.log(`❌ ${testName} (${duration}ms) - ${error.message}`);
    }
  }

  private async testErrorHandling(): Promise<void> {
    const testName = 'Error Handling - Invalid JSON';
    const startTime = Date.now();

    try {
      const response = await fetch(`${BASE_URL}/search-guidelines`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': 'test-anon-key'
        },
        body: 'invalid json'
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      const isValidError = 
        response.status === 400 &&
        responseData.success === false &&
        responseData.error &&
        responseData.message;

      this.results.push({
        name: testName,
        passed: isValidError,
        duration,
        error: !isValidError ? 'Error response format incorrect' : undefined
      });

      console.log(`${isValidError ? '✅' : '❌'} ${testName} (${duration}ms)`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name: testName,
        passed: false,
        duration,
        error: error.message
      });

      console.log(`❌ ${testName} (${duration}ms) - ${error.message}`);
    }
  }

  private validateSearchGuidelinesResponse(response: any): boolean {
    return (
      response.success === true &&
      response.data &&
      Array.isArray(response.data.results) &&
      typeof response.data.total_found === 'number' &&
      response.data.search_metadata &&
      response.metadata &&
      typeof response.metadata.timestamp === 'string'
    );
  }

  private validateGenerateNarrativeResponse(response: any): boolean {
    return (
      response.success === true &&
      response.data &&
      typeof response.data.narrative === 'string' &&
      Array.isArray(response.data.supporting_guidelines) &&
      Array.isArray(response.data.recommendations) &&
      typeof response.data.confidence_score === 'number' &&
      response.metadata &&
      typeof response.metadata.timestamp === 'string'
    );
  }

  private validateAnalyzeClaimResponse(response: any): boolean {
    return (
      response.success === true &&
      response.data &&
      response.data.claim_analysis &&
      response.data.carrier_info &&
      Array.isArray(response.data.procedures_analyzed) &&
      response.metadata &&
      typeof response.metadata.timestamp === 'string'
    );
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / total;

    console.log(`Tests Passed: ${passed}/${total}`);
    console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);
    console.log(`Average Duration: ${Math.round(avgDuration)}ms\n`);

    // Show failed tests
    const failed = this.results.filter(r => !r.passed);
    if (failed.length > 0) {
      console.log('❌ Failed Tests:');
      failed.forEach(test => {
        console.log(`  - ${test.name}: ${test.error}`);
      });
      console.log('');
    }

    // Show performance summary
    console.log('⚡ Performance Summary:');
    this.results.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`  ${status} ${test.name}: ${test.duration}ms`);
    });
  }
}

// Run tests if this file is executed directly
if (import.meta.main) {
  const tester = new EdgeFunctionTester();
  await tester.runAllTests();
}
