/**
 * Test data for Edge Functions
 */

export const testSearchGuidelinesRequest = {
  query: "D1110 prophylaxis cleaning coverage",
  carrier: "Delta Dental",
  category: "preventive",
  limit: 5
};

export const testGenerateNarrativeRequest = {
  claim_data: {
    patient_info: {
      name: "<PERSON>",
      dob: "1985-06-15",
      member_id: "DD123456789"
    },
    procedures: [
      {
        code: "D1110",
        description: "Prophylaxis - adult",
        date: "2024-12-20",
        tooth: "",
        surfaces: []
      },
      {
        code: "D0150",
        description: "Comprehensive oral evaluation",
        date: "2024-12-20",
        tooth: "",
        surfaces: []
      }
    ],
    diagnosis: "Gingivitis, chronic",
    medical_history: "No significant medical history",
    treatment_plan: "Routine prophylaxis and oral hygiene instruction"
  },
  carrier: "Delta Dental",
  narrative_type: "standard"
};

export const testAnalyzeClaimRequest = {
  claim_data: {
    patient_info: {
      name: "<PERSON>",
      dob: "1990-03-22",
      member_id: "BC987654321",
      age: 34
    },
    procedures: [
      {
        code: "D2150",
        description: "Amalgam - two surfaces, primary or permanent",
        date: "2024-12-20",
        tooth: "14",
        surfaces: ["M", "O"],
        fee: 185.00
      },
      {
        code: "D0220",
        description: "Intraoral - periapical first radiographic image",
        date: "2024-12-20",
        tooth: "14",
        surfaces: [],
        fee: 45.00
      }
    ],
    diagnosis: "Caries, dental, chronic",
    medical_history: "No allergies, no medications",
    treatment_plan: "Restore tooth #14 with amalgam filling"
  },
  carrier: "Blue Cross Blue Shield",
  analysis_type: "comprehensive"
};

export const testValidateCoverageRequest = {
  carrier: "Aetna",
  procedure_codes: ["D1110", "D0150", "D0210"],
  patient_info: {
    age: 45,
    member_id: "AET456789"
  }
};

export const testGlossaryLookupRequest = {
  term: "prophylaxis",
  exact_match: false
};

// Expected response structures for validation
export const expectedSearchGuidelinesResponse = {
  success: true,
  data: {
    results: [],
    total_found: 0,
    search_metadata: {
      query: "",
      similarity_threshold: 0.2
    }
  },
  metadata: {
    timestamp: "",
    processing_time_ms: 0
  }
};

export const expectedGenerateNarrativeResponse = {
  success: true,
  data: {
    narrative: "",
    supporting_guidelines: [],
    recommendations: [],
    confidence_score: 0
  },
  metadata: {
    timestamp: "",
    processing_time_ms: 0
  }
};

export const expectedAnalyzeClaimResponse = {
  success: true,
  data: {
    claim_analysis: {
      overall_assessment: {},
      procedure_analysis: [],
      risk_factors: [],
      recommendations: [],
      approval_likelihood: ""
    },
    carrier_info: {
      name: "",
      payer_id: ""
    },
    procedures_analyzed: []
  },
  metadata: {
    timestamp: "",
    processing_time_ms: 0
  }
};

// Test environment variables
export const testEnvVars = {
  SUPABASE_URL: "http://localhost:54321",
  SUPABASE_SERVICE_ROLE_KEY: "test-service-role-key",
  SUPABASE_ANON_KEY: "test-anon-key",
  OPENAI_API_KEY: "test-openai-key"
};

// Mock responses for external services
export const mockOpenAIResponse = {
  choices: [
    {
      message: {
        content: "This is a test narrative generated for the dental claim. The patient requires routine prophylaxis and comprehensive examination as part of their preventive care plan."
      }
    }
  ]
};

export const mockGuidelinesSearchResult = [
  {
    id: 1,
    title: "Prophylaxis Coverage Guidelines",
    category: "preventive",
    carrier: "Delta Dental",
    similarity_score: 0.85,
    content: "Prophylaxis (D1110) is covered twice per calendar year for adult patients. No prior authorization required for routine cleanings."
  },
  {
    id: 2,
    title: "Comprehensive Exam Requirements",
    category: "diagnostic",
    carrier: "Delta Dental", 
    similarity_score: 0.78,
    content: "Comprehensive oral evaluation (D0150) is covered once per year. Must include periodontal charting and oral cancer screening."
  }
];

export const mockCarrierInfo = {
  id: 1,
  name: "Delta Dental",
  type: "dental",
  payer_id: "DD001",
  claims_address: "123 Insurance Way, Claims City, ST 12345",
  phone_number: "1-800-DENTAL1",
  website: "https://www.deltadental.com",
  contact_info: {
    customer_service: "1-800-DENTAL1",
    provider_services: "1-800-DENTAL2"
  }
};

export const mockProcedureInfo = [
  {
    id: 1,
    cdt_code: "D1110",
    name: "Prophylaxis - adult",
    description: "Adult prophylaxis - removal of plaque, calculus and stains",
    category: "preventive",
    found: true
  },
  {
    id: 2,
    cdt_code: "D0150", 
    name: "Comprehensive oral evaluation - new or established patient",
    description: "Comprehensive oral evaluation including oral cancer screening",
    category: "diagnostic",
    found: true
  }
];
