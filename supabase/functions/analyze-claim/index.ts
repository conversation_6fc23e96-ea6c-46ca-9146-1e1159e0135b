import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { EdgeDatabaseUtils } from '../_shared/database/index.ts';
import { AnalyzeClaimSchema, validateRequestBody } from '../_shared/validation/index.ts';
import { ResponseBuilder, parseRequestBody, logRequest, logResponse } from '../_shared/responses/index.ts';
import { withErrorHandling } from '../_shared/errors/index.ts';
import { getConfig, handleCorsPreflightRequest, getLogger } from '../_shared/config/index.ts';

const config = getConfig();
const logger = getLogger();
const dbUtils = EdgeDatabaseUtils.getInstance();

/**
 * Analyze dental insurance claim for coverage, medical necessity, and approval likelihood
 */
async function analyzeClaimHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  logger.logRequest(request);

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return handleCorsPreflightRequest(request, config);
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // Parse and validate request body
  const body = await parseRequestBody(request);
  const validatedData = validateRequestBody(AnalyzeClaimSchema, body);
  const { claim_data, carrier, analysis_type } = validatedData;

  logger.info('Analyzing claim', {
    carrier,
    analysis_type,
    procedures_count: claim_data.procedures.length,
    patient_age: claim_data.patient_info.age
  });

  // Look up carrier information
  const carrierInfo = await dbUtils.lookupCarrier(carrier);
  if (!carrierInfo) {
    return ResponseBuilder.notFound(`Carrier "${carrier}"`);
  }

  // Look up procedure information
  const procedureCodes = claim_data.procedures.map(p => p.code);
  const procedureInfo = await dbUtils.lookupProcedures(procedureCodes);

  // Perform comprehensive analysis
  const analysisResults = await performClaimAnalysis(
    claim_data,
    carrierInfo,
    procedureInfo,
    analysis_type
  );

  const duration = Date.now() - startTime;
  logResponse('POST', '/analyze-claim', 200, duration);

  return ResponseBuilder.success({
    claim_analysis: analysisResults,
    carrier_info: {
      name: carrierInfo.name,
      payer_id: carrierInfo.payer_id
    },
    procedures_analyzed: procedureInfo,
    analysis_metadata: {
      analysis_type,
      processing_time_ms: duration,
      procedures_count: procedureCodes.length,
      guidelines_consulted: analysisResults.guidelines_consulted || 0
    }
  });
}

/**
 * Perform comprehensive claim analysis
 */
async function performClaimAnalysis(
  claimData: any,
  carrierInfo: any,
  procedureInfo: any[],
  analysisType: string
): Promise<any> {
  const analysis: any = {
    overall_assessment: {},
    procedure_analysis: [],
    risk_factors: [],
    recommendations: [],
    approval_likelihood: 'unknown'
  };

  // Analyze each procedure
  for (const procedure of claimData.procedures) {
    const procInfo = procedureInfo.find(p => p.cdt_code === procedure.code);
    const procAnalysis = await analyzeProcedure(
      procedure,
      procInfo,
      claimData,
      carrierInfo,
      analysisType
    );
    analysis.procedure_analysis.push(procAnalysis);
  }

  // Perform frequency analysis if requested
  if (analysisType === 'frequency' || analysisType === 'comprehensive') {
    const frequencyAnalysis = await analyzeFrequencyLimitations(
      claimData.procedures,
      claimData.prior_treatments || [],
      carrierInfo
    );
    analysis.frequency_analysis = frequencyAnalysis;
  }

  // Perform medical necessity analysis
  if (analysisType === 'medical_necessity' || analysisType === 'comprehensive') {
    const medicalNecessityAnalysis = analyzeMedicalNecessity(
      claimData,
      analysis.procedure_analysis
    );
    analysis.medical_necessity = medicalNecessityAnalysis;
  }

  // Calculate overall approval likelihood
  analysis.approval_likelihood = calculateApprovalLikelihood(analysis);

  // Generate risk factors
  analysis.risk_factors = identifyRiskFactors(claimData, analysis);

  // Generate recommendations
  analysis.recommendations = generateAnalysisRecommendations(claimData, analysis);

  // Overall assessment
  analysis.overall_assessment = {
    total_procedures: claimData.procedures.length,
    high_risk_procedures: analysis.procedure_analysis.filter(p => p.risk_level === 'high').length,
    coverage_concerns: analysis.procedure_analysis.filter(p => p.coverage_status === 'questionable').length,
    estimated_approval_rate: calculateEstimatedApprovalRate(analysis)
  };

  return analysis;
}

/**
 * Analyze individual procedure
 */
async function analyzeProcedure(
  procedure: any,
  procInfo: any,
  claimData: any,
  carrierInfo: any,
  analysisType: string
): Promise<any> {
  const analysis: any = {
    procedure_code: procedure.code,
    procedure_name: procInfo?.name || 'Unknown procedure',
    coverage_status: 'unknown',
    risk_level: 'low',
    concerns: [],
    supporting_factors: []
  };

  // Check if procedure exists in database
  if (!procInfo?.found) {
    analysis.coverage_status = 'not_covered';
    analysis.risk_level = 'high';
    analysis.concerns.push('Procedure code not found in database');
    return analysis;
  }

  // Search for relevant guidelines
  const searchQuery = `${procedure.code} ${procInfo.name} coverage requirements`;
  try {
    const guidelines = await dbUtils.searchGuidelines(searchQuery, {
      limit: 3,
      carrierFilter: carrierInfo.name,
      similarityThreshold: 0.3
    });

    if (guidelines.length > 0) {
      analysis.coverage_status = 'covered';
      analysis.supporting_factors.push(`Found ${guidelines.length} relevant guidelines`);
      
      // Analyze guideline content for specific concerns
      for (const guideline of guidelines) {
        const content = guideline.content.toLowerCase();
        
        // Check for age restrictions
        if (content.includes('age') && claimData.patient_info.age) {
          const ageRestrictions = extractAgeRestrictions(content);
          if (ageRestrictions && !checkAgeCompliance(claimData.patient_info.age, ageRestrictions)) {
            analysis.concerns.push('May not meet age requirements');
            analysis.risk_level = 'medium';
          }
        }
        
        // Check for frequency limitations
        if (content.includes('frequency') || content.includes('limitation')) {
          analysis.concerns.push('Subject to frequency limitations');
          analysis.risk_level = 'medium';
        }
        
        // Check for prior authorization requirements
        if (content.includes('prior authorization') || content.includes('predetermination')) {
          analysis.concerns.push('May require prior authorization');
          analysis.risk_level = 'medium';
        }
      }
    } else {
      analysis.coverage_status = 'questionable';
      analysis.risk_level = 'medium';
      analysis.concerns.push('No specific coverage guidelines found');
    }
  } catch (error) {
    logger.warn(`Failed to search guidelines for procedure ${procedure.code}`, error);
    analysis.coverage_status = 'unknown';
    analysis.concerns.push('Unable to verify coverage guidelines');
  }

  // Additional procedure-specific analysis
  if (procedure.code.startsWith('D0')) {
    // Diagnostic procedures - generally well covered
    analysis.supporting_factors.push('Diagnostic procedure - typically well covered');
  } else if (procedure.code.startsWith('D1')) {
    // Preventive procedures
    analysis.supporting_factors.push('Preventive procedure - usually covered');
  } else if (procedure.code.startsWith('D7')) {
    // Oral surgery - higher scrutiny
    analysis.risk_level = 'medium';
    analysis.concerns.push('Surgical procedure - may require additional documentation');
  } else if (procedure.code.startsWith('D6')) {
    // Prosthodontics - often has limitations
    analysis.risk_level = 'medium';
    analysis.concerns.push('Prosthodontic procedure - check benefit limitations');
  }

  return analysis;
}

/**
 * Analyze frequency limitations
 */
async function analyzeFrequencyLimitations(
  currentProcedures: any[],
  priorTreatments: any[],
  carrierInfo: any
): Promise<any> {
  const frequencyAnalysis = {
    violations: [],
    warnings: [],
    compliant_procedures: []
  };

  // Common frequency rules (simplified)
  const frequencyRules = {
    'D1110': { period: 'months', limit: 6, description: 'Prophylaxis - every 6 months' },
    'D0150': { period: 'months', limit: 12, description: 'Comprehensive exam - annually' },
    'D0210': { period: 'months', limit: 12, description: 'Intraoral X-rays - annually' },
    'D0330': { period: 'months', limit: 36, description: 'Panoramic X-ray - every 3 years' }
  };

  for (const procedure of currentProcedures) {
    const rule = frequencyRules[procedure.code];
    if (rule) {
      const recentTreatments = priorTreatments.filter(t => 
        t.code === procedure.code &&
        isWithinPeriod(t.date, procedure.date, rule.period, rule.limit)
      );

      if (recentTreatments.length > 0) {
        frequencyAnalysis.violations.push({
          procedure_code: procedure.code,
          rule: rule.description,
          last_treatment: recentTreatments[0].date,
          violation_type: 'frequency_exceeded'
        });
      } else {
        frequencyAnalysis.compliant_procedures.push(procedure.code);
      }
    }
  }

  return frequencyAnalysis;
}

/**
 * Analyze medical necessity
 */
function analyzeMedicalNecessity(claimData: any, procedureAnalysis: any[]): any {
  const analysis = {
    overall_score: 0,
    supporting_factors: [],
    concerns: [],
    documentation_requirements: []
  };

  // Check for diagnosis
  if (claimData.diagnosis) {
    analysis.supporting_factors.push('Clinical diagnosis provided');
    analysis.overall_score += 20;
  } else {
    analysis.concerns.push('No diagnosis provided');
    analysis.documentation_requirements.push('Provide clinical diagnosis');
  }

  // Check for medical history
  if (claimData.medical_history) {
    analysis.supporting_factors.push('Medical history documented');
    analysis.overall_score += 15;
  }

  // Check for treatment plan
  if (claimData.treatment_plan) {
    analysis.supporting_factors.push('Treatment plan documented');
    analysis.overall_score += 15;
  }

  // Analyze procedure sequence and logic
  const procedureCodes = claimData.procedures.map(p => p.code);
  if (hasLogicalTreatmentSequence(procedureCodes)) {
    analysis.supporting_factors.push('Logical treatment sequence');
    analysis.overall_score += 20;
  }

  // Cap the score at 100
  analysis.overall_score = Math.min(analysis.overall_score, 100);

  return analysis;
}

/**
 * Helper functions
 */
function extractAgeRestrictions(content: string): any {
  // Simplified age restriction extraction
  const ageMatch = content.match(/(\d+)\s*years?\s*(old|age)/i);
  return ageMatch ? parseInt(ageMatch[1]) : null;
}

function checkAgeCompliance(patientAge: number, restriction: number): boolean {
  // Simplified age compliance check
  return patientAge >= restriction;
}

function isWithinPeriod(date1: string, date2: string, period: string, limit: number): boolean {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffMs = Math.abs(d2.getTime() - d1.getTime());
  const diffMonths = diffMs / (1000 * 60 * 60 * 24 * 30.44); // Approximate months
  
  return diffMonths < limit;
}

function hasLogicalTreatmentSequence(codes: string[]): boolean {
  // Simplified logic - check if diagnostic codes come before treatment codes
  const diagnosticCodes = codes.filter(c => c.startsWith('D0'));
  const treatmentCodes = codes.filter(c => !c.startsWith('D0'));
  
  return diagnosticCodes.length > 0 || treatmentCodes.length <= 2;
}

function calculateApprovalLikelihood(analysis: any): string {
  const highRiskCount = analysis.procedure_analysis.filter(p => p.risk_level === 'high').length;
  const totalProcedures = analysis.procedure_analysis.length;
  
  if (highRiskCount === 0) return 'high';
  if (highRiskCount / totalProcedures < 0.3) return 'medium';
  return 'low';
}

function calculateEstimatedApprovalRate(analysis: any): number {
  const baseRate = 85; // Base approval rate
  const highRiskPenalty = analysis.procedure_analysis.filter(p => p.risk_level === 'high').length * 15;
  const mediumRiskPenalty = analysis.procedure_analysis.filter(p => p.risk_level === 'medium').length * 5;
  
  return Math.max(baseRate - highRiskPenalty - mediumRiskPenalty, 10);
}

function identifyRiskFactors(claimData: any, analysis: any): string[] {
  const riskFactors = [];
  
  if (!claimData.diagnosis) riskFactors.push('Missing diagnosis');
  if (!claimData.medical_history) riskFactors.push('No medical history provided');
  if (analysis.frequency_analysis?.violations?.length > 0) riskFactors.push('Frequency violations detected');
  if (analysis.procedure_analysis.some(p => p.coverage_status === 'questionable')) {
    riskFactors.push('Some procedures have questionable coverage');
  }
  
  return riskFactors;
}

function generateAnalysisRecommendations(claimData: any, analysis: any): string[] {
  const recommendations = [];
  
  if (!claimData.diagnosis) {
    recommendations.push('Include detailed clinical diagnosis');
  }
  
  if (analysis.procedure_analysis.some(p => p.risk_level === 'high')) {
    recommendations.push('Provide additional documentation for high-risk procedures');
  }
  
  if (analysis.frequency_analysis?.violations?.length > 0) {
    recommendations.push('Review frequency limitations before submitting');
  }
  
  recommendations.push('Ensure all clinical documentation is complete');
  recommendations.push('Consider submitting predetermination for complex cases');
  
  return recommendations;
}

// Serve the function
serve(withErrorHandling(analyzeClaimHandler, 'analyze-claim'));
