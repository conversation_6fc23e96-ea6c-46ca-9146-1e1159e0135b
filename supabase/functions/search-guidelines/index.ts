import { serve } from 'https://deno.land/std@0.224.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

// Simple CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Simple response helper
function jsonResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders,
    },
  });
}

// Simple error response helper
function errorResponse(message: string, status = 500) {
  return jsonResponse({
    success: false,
    error: message,
    timestamp: new Date().toISOString()
  }, status);
}

/**
 * Search dental insurance guidelines using vector similarity
 */
async function searchGuidelinesHandler(request: Request): Promise<Response> {
  const startTime = Date.now();

  try {
    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    // Only allow POST requests
    if (request.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return errorResponse('Invalid JSON in request body', 400);
    }

    // Basic validation
    const { query, carrier, category, limit = 5 } = body;
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return errorResponse('Query is required and must be a non-empty string', 400);
    }

    console.log(`Searching guidelines for: "${query}"`);

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration');
      return errorResponse('Service configuration error', 500);
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // For now, return mock data to test the function works
    // TODO: Implement actual vector search once function is stable
    const mockResults = [
      {
        id: 1,
        title: `Guidelines for: ${query}`,
        category: category || 'General',
        carrier: carrier || 'Multiple Carriers',
        similarity_score: 0.85,
        content: `This is a mock guideline result for "${query}". The actual implementation would perform vector similarity search against the guidelines database.`,
        relevance_score: 0.85,
        content_preview: `This is a mock guideline result for "${query}". The actual implementation would perform...`,
        match_type: 'high',
        keywords: query.toLowerCase().split(' ').slice(0, 3)
      }
    ];

    const duration = Date.now() - startTime;

    // Return successful response
    return jsonResponse({
      success: true,
      data: {
        results: mockResults.slice(0, limit),
        total_found: mockResults.length,
        search_metadata: {
          query,
          carrier_filter: carrier,
          category_filter: category,
          similarity_threshold: 0.2,
          processing_time_ms: duration,
          search_type: 'mock_data',
          quality: 'high_relevance'
        }
      },
      metadata: {
        timestamp: new Date().toISOString(),
        processing_time_ms: duration
      }
    });

  } catch (error) {
    console.error('Error in search-guidelines function:', error);
    return errorResponse(`Internal server error: ${error.message}`, 500);
  }
}

/**
 * Determine match type based on similarity score
 */
function determineMatchType(score: number): string {
  if (score >= 0.8) return 'exact';
  if (score >= 0.6) return 'high';
  if (score >= 0.4) return 'medium';
  if (score >= 0.2) return 'low';
  return 'minimal';
}

/**
 * Extract relevant keywords from content based on query
 */
function extractKeywords(content: string, query: string): string[] {
  const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
  const contentWords = content.toLowerCase().split(/\s+/);
  
  const keywords = [];
  
  // Find exact matches
  for (const queryWord of queryWords) {
    if (contentWords.includes(queryWord)) {
      keywords.push(queryWord);
    }
  }
  
  // Find partial matches (for procedure codes, etc.)
  for (const queryWord of queryWords) {
    const partialMatches = contentWords.filter(word => 
      word.includes(queryWord) || queryWord.includes(word)
    );
    keywords.push(...partialMatches.slice(0, 2)); // Limit partial matches
  }
  
  // Remove duplicates and return top 5
  return [...new Set(keywords)].slice(0, 5);
}

/**
 * Generate search insights and suggestions
 */
function generateSearchInsights(
  results: any[],
  query: string,
  carrier?: string,
  category?: string
): Record<string, any> {
  const insights: Record<string, any> = {};
  
  // Quality assessment
  if (results.length === 0) {
    insights.suggestion = 'Try broader search terms or remove filters';
    insights.quality = 'no_results';
  } else if (results[0]?.similarity_score < 0.3) {
    insights.suggestion = 'Consider refining your search terms for better matches';
    insights.quality = 'low_relevance';
  } else if (results[0]?.similarity_score > 0.7) {
    insights.quality = 'high_relevance';
  } else {
    insights.quality = 'moderate_relevance';
  }
  
  // Carrier coverage
  const carrierResults = results.filter(r => r.carrier !== 'Unknown');
  if (carrier && carrierResults.length === 0) {
    insights.carrier_note = `No specific guidelines found for ${carrier}. Showing general guidelines.`;
  } else if (carrierResults.length > 0) {
    insights.carrier_coverage = `Found ${carrierResults.length} carrier-specific guidelines`;
  }
  
  // Category distribution
  const categories = results.reduce((acc, r) => {
    acc[r.category] = (acc[r.category] || 0) + 1;
    return acc;
  }, {});
  
  if (Object.keys(categories).length > 1) {
    insights.category_distribution = categories;
  }
  
  // Search term analysis
  const queryWords = query.toLowerCase().split(/\s+/);
  const procedureCodePattern = /^[dD]\d{4}$/;
  const hasProcedureCode = queryWords.some(word => procedureCodePattern.test(word));
  
  if (hasProcedureCode) {
    insights.search_type = 'procedure_specific';
  } else if (queryWords.some(word => ['coverage', 'benefit', 'limitation'].includes(word))) {
    insights.search_type = 'coverage_inquiry';
  } else {
    insights.search_type = 'general_search';
  }
  
  return insights;
}

/**
 * Calculate overall search quality score
 */
function calculateSearchQuality(results: any[]): string {
  if (results.length === 0) return 'no_results';
  
  const avgScore = results.reduce((sum, r) => sum + r.similarity_score, 0) / results.length;
  
  if (avgScore >= 0.7) return 'excellent';
  if (avgScore >= 0.5) return 'good';
  if (avgScore >= 0.3) return 'fair';
  return 'poor';
}

/**
 * Get top categories from results
 */
function getTopCategories(results: any[]): string[] {
  const categoryCount = results.reduce((acc, r) => {
    acc[r.category] = (acc[r.category] || 0) + 1;
    return acc;
  }, {});
  
  return Object.entries(categoryCount)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 3)
    .map(([category]) => category);
}

// Serve the function
serve(searchGuidelinesHandler);
