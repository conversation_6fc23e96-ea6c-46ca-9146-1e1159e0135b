import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { EdgeDatabaseUtils } from '../_shared/database/index.ts';
import { SearchGuidelinesSchema, validateRequestBody } from '../_shared/validation/index.ts';
import { ResponseBuilder, parseRequestBody, logRequest, logResponse, createSearchResponse } from '../_shared/responses/index.ts';
import { withErrorHandling } from '../_shared/errors/index.ts';
import { getConfig, handleCorsPreflightRequest, getLogger } from '../_shared/config/index.ts';
import type { SearchGuidelinesRequest, SearchGuidelinesResponse } from '../_shared/types/index.ts';

const config = getConfig();
const logger = getLogger();
const dbUtils = EdgeDatabaseUtils.getInstance();

/**
 * Search dental insurance guidelines using vector similarity
 */
async function searchGuidelinesHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  logger.logRequest(request);

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return handleCorsPreflightRequest(request, config);
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // Parse and validate request body
  const body = await parseRequestBody(request);
  const validatedData = validateRequestBody(SearchGuidelinesSchema, body);
  const { query, carrier, category, limit } = validatedData;

  logger.info('Searching guidelines', {
    query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
    carrier,
    category,
    limit
  });

  // Perform vector search
  const searchResults = await dbUtils.searchGuidelines(query, {
    limit,
    similarityThreshold: 0.2, // Lower threshold for broader results
    carrierFilter: carrier,
    categoryFilter: category
  });

  // Enhance results with additional metadata
  const enhancedResults = searchResults.map(result => ({
    ...result,
    relevance_score: Math.round(result.similarity_score * 100) / 100,
    content_preview: result.content.length > 300 
      ? result.content.substring(0, 300) + '...'
      : result.content,
    match_type: determineMatchType(result.similarity_score),
    keywords: extractKeywords(result.content, query)
  }));

  // Sort by similarity score (highest first)
  enhancedResults.sort((a, b) => b.similarity_score - a.similarity_score);

  // Generate search insights
  const searchInsights = generateSearchInsights(enhancedResults, query, carrier, category);

  const duration = Date.now() - startTime;
  logResponse('POST', '/search-guidelines', 200, duration);

  const responseData: SearchGuidelinesResponse['data'] = {
    results: enhancedResults,
    total_found: enhancedResults.length,
    search_metadata: {
      query,
      carrier_filter: carrier,
      category_filter: category,
      similarity_threshold: 0.2,
      processing_time_ms: duration,
      ...searchInsights
    }
  };

  return ResponseBuilder.success(responseData, {
    processing_time_ms: duration,
    search_quality: calculateSearchQuality(enhancedResults),
    has_carrier_specific_results: enhancedResults.some(r => r.carrier !== 'Unknown'),
    top_categories: getTopCategories(enhancedResults)
  });
}

/**
 * Determine match type based on similarity score
 */
function determineMatchType(score: number): string {
  if (score >= 0.8) return 'exact';
  if (score >= 0.6) return 'high';
  if (score >= 0.4) return 'medium';
  if (score >= 0.2) return 'low';
  return 'minimal';
}

/**
 * Extract relevant keywords from content based on query
 */
function extractKeywords(content: string, query: string): string[] {
  const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
  const contentWords = content.toLowerCase().split(/\s+/);
  
  const keywords = [];
  
  // Find exact matches
  for (const queryWord of queryWords) {
    if (contentWords.includes(queryWord)) {
      keywords.push(queryWord);
    }
  }
  
  // Find partial matches (for procedure codes, etc.)
  for (const queryWord of queryWords) {
    const partialMatches = contentWords.filter(word => 
      word.includes(queryWord) || queryWord.includes(word)
    );
    keywords.push(...partialMatches.slice(0, 2)); // Limit partial matches
  }
  
  // Remove duplicates and return top 5
  return [...new Set(keywords)].slice(0, 5);
}

/**
 * Generate search insights and suggestions
 */
function generateSearchInsights(
  results: any[],
  query: string,
  carrier?: string,
  category?: string
): Record<string, any> {
  const insights: Record<string, any> = {};
  
  // Quality assessment
  if (results.length === 0) {
    insights.suggestion = 'Try broader search terms or remove filters';
    insights.quality = 'no_results';
  } else if (results[0]?.similarity_score < 0.3) {
    insights.suggestion = 'Consider refining your search terms for better matches';
    insights.quality = 'low_relevance';
  } else if (results[0]?.similarity_score > 0.7) {
    insights.quality = 'high_relevance';
  } else {
    insights.quality = 'moderate_relevance';
  }
  
  // Carrier coverage
  const carrierResults = results.filter(r => r.carrier !== 'Unknown');
  if (carrier && carrierResults.length === 0) {
    insights.carrier_note = `No specific guidelines found for ${carrier}. Showing general guidelines.`;
  } else if (carrierResults.length > 0) {
    insights.carrier_coverage = `Found ${carrierResults.length} carrier-specific guidelines`;
  }
  
  // Category distribution
  const categories = results.reduce((acc, r) => {
    acc[r.category] = (acc[r.category] || 0) + 1;
    return acc;
  }, {});
  
  if (Object.keys(categories).length > 1) {
    insights.category_distribution = categories;
  }
  
  // Search term analysis
  const queryWords = query.toLowerCase().split(/\s+/);
  const procedureCodePattern = /^[dD]\d{4}$/;
  const hasProcedureCode = queryWords.some(word => procedureCodePattern.test(word));
  
  if (hasProcedureCode) {
    insights.search_type = 'procedure_specific';
  } else if (queryWords.some(word => ['coverage', 'benefit', 'limitation'].includes(word))) {
    insights.search_type = 'coverage_inquiry';
  } else {
    insights.search_type = 'general_search';
  }
  
  return insights;
}

/**
 * Calculate overall search quality score
 */
function calculateSearchQuality(results: any[]): string {
  if (results.length === 0) return 'no_results';
  
  const avgScore = results.reduce((sum, r) => sum + r.similarity_score, 0) / results.length;
  
  if (avgScore >= 0.7) return 'excellent';
  if (avgScore >= 0.5) return 'good';
  if (avgScore >= 0.3) return 'fair';
  return 'poor';
}

/**
 * Get top categories from results
 */
function getTopCategories(results: any[]): string[] {
  const categoryCount = results.reduce((acc, r) => {
    acc[r.category] = (acc[r.category] || 0) + 1;
    return acc;
  }, {});
  
  return Object.entries(categoryCount)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 3)
    .map(([category]) => category);
}

// Serve the function
serve(withErrorHandling(searchGuidelinesHandler, 'search-guidelines'));
