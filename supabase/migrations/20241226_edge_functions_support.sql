-- SQL functions to support Edge Functions vector search operations

-- Function for basic vector similarity search
CREATE OR REPLACE FUNCTION vector_search(
  query_embedding vector(1536),
  similarity_threshold float DEFAULT 0.1,
  match_count int DEFAULT 5
)
RETURNS TABLE (
  id bigint,
  content_type text,
  content_id bigint,
  similarity_score float,
  metadata jsonb
)
LANGUAGE sql STABLE
AS $$
  SELECT 
    e.id,
    e.content_type,
    e.content_id,
    1 - (e.embedding <=> query_embedding) as similarity_score,
    e.metadata
  FROM embeddings e
  WHERE (1 - (e.embedding <=> query_embedding)) >= similarity_threshold
  ORDER BY e.embedding <=> query_embedding
  LIMIT match_count;
$$;

-- Function for guideline search with metadata joins
CREATE OR REPLACE FUNCTION search_guidelines_with_metadata(
  query_embedding vector(1536),
  similarity_threshold float DEFAULT 0.3,
  match_count int DEFAULT 5,
  carrier_filter text DEFAULT NULL,
  category_filter text DEFAULT NULL
)
RETURNS TABLE (
  id bigint,
  title text,
  category text,
  carrier text,
  similarity_score float,
  content text
)
LANGUAGE sql STABLE
AS $$
  SELECT 
    g.id,
    g.title,
    g.category,
    COALESCE(ic.carrier_name, 'Unknown') as carrier,
    1 - (e.embedding <=> query_embedding) as similarity_score,
    CASE 
      WHEN jsonb_typeof(g.content) = 'string' THEN g.content::text
      ELSE g.content->>'text'
    END as content
  FROM embeddings e
  JOIN guidelines g ON e.content_id = g.id AND e.content_type = 'guideline'
  LEFT JOIN insurance_carriers ic ON g.carrier_id = ic.id
  WHERE (1 - (e.embedding <=> query_embedding)) >= similarity_threshold
    AND (carrier_filter IS NULL OR ic.carrier_name ILIKE '%' || carrier_filter || '%')
    AND (category_filter IS NULL OR g.category ILIKE '%' || category_filter || '%')
  ORDER BY e.embedding <=> query_embedding
  LIMIT match_count;
$$;

-- Function to check if vector extension is enabled
CREATE OR REPLACE FUNCTION check_vector_extension()
RETURNS boolean
LANGUAGE sql STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'vector'
  );
$$;

-- Function to get comprehensive database statistics
CREATE OR REPLACE FUNCTION get_database_stats()
RETURNS TABLE (
  total_guidelines bigint,
  total_embeddings bigint,
  total_carriers bigint,
  vector_extension_enabled boolean,
  last_updated timestamp with time zone
)
LANGUAGE sql STABLE
AS $$
  SELECT 
    (SELECT COUNT(*) FROM guidelines) as total_guidelines,
    (SELECT COUNT(*) FROM embeddings) as total_embeddings,
    (SELECT COUNT(*) FROM insurance_carriers) as total_carriers,
    check_vector_extension() as vector_extension_enabled,
    NOW() as last_updated;
$$;

-- Function for procedure coverage analysis
CREATE OR REPLACE FUNCTION analyze_procedure_coverage(
  carrier_name_param text,
  procedure_codes text[]
)
RETURNS TABLE (
  procedure_code text,
  procedure_name text,
  coverage_found boolean,
  relevant_guidelines jsonb
)
LANGUAGE plpgsql STABLE
AS $$
DECLARE
  carrier_id_var bigint;
BEGIN
  -- Get carrier ID
  SELECT id INTO carrier_id_var 
  FROM insurance_carriers 
  WHERE carrier_name ILIKE '%' || carrier_name_param || '%'
  LIMIT 1;

  -- Return procedure analysis
  RETURN QUERY
  SELECT 
    p.cdt_code as procedure_code,
    p.name as procedure_name,
    (g.id IS NOT NULL) as coverage_found,
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'guideline_id', g.id,
          'title', g.title,
          'category', g.category
        )
      ) FILTER (WHERE g.id IS NOT NULL),
      '[]'::jsonb
    ) as relevant_guidelines
  FROM unnest(procedure_codes) AS proc_code
  LEFT JOIN procedures p ON p.cdt_code = proc_code
  LEFT JOIN guidelines g ON (
    g.carrier_id = carrier_id_var 
    AND (
      g.content::text ILIKE '%' || p.cdt_code || '%'
      OR g.content::text ILIKE '%' || p.name || '%'
    )
  )
  GROUP BY p.cdt_code, p.name;
END;
$$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_embeddings_content_type ON embeddings(content_type);
CREATE INDEX IF NOT EXISTS idx_embeddings_vector ON embeddings USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_guidelines_carrier_id ON guidelines(carrier_id);
CREATE INDEX IF NOT EXISTS idx_guidelines_category ON guidelines(category);
CREATE INDEX IF NOT EXISTS idx_insurance_carriers_name ON insurance_carriers(carrier_name);
CREATE INDEX IF NOT EXISTS idx_procedures_cdt_code ON procedures(cdt_code);

-- Grant necessary permissions for Edge Functions
GRANT EXECUTE ON FUNCTION vector_search TO service_role;
GRANT EXECUTE ON FUNCTION search_guidelines_with_metadata TO service_role;
GRANT EXECUTE ON FUNCTION check_vector_extension TO service_role;
GRANT EXECUTE ON FUNCTION get_database_stats TO service_role;
GRANT EXECUTE ON FUNCTION analyze_procedure_coverage TO service_role;
